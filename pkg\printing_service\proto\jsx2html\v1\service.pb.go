// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: jsx2html/v1/service.proto

package jsx2htmlv1

import (
	reflect "reflect"
	v1 "snap-printer/gen/proto/jsx2html/v1"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_jsx2html_v1_service_proto protoreflect.FileDescriptor

const file_jsx2html_v1_service_proto_rawDesc = "" +
	"\n" +
	"\x19jsx2html/v1/service.proto\x12\vjsx2html.v1\x1a\x17jsx2html/v1/model.proto2Z\n" +
	"\x0fJsx2HtmlService\x12G\n" +
	"\bGenerate\x12\x1c.jsx2html.v1.GenerateRequest\x1a\x1d.jsx2html.v1.GenerateResponseB/Z-snap-printer/gen/proto/jsx2html/v1;jsx2htmlv1b\x06proto3"

var file_jsx2html_v1_service_proto_goTypes = []any{
	(*v1.GenerateRequest)(nil),  // 0: jsx2html.v1.GenerateRequest
	(*v1.GenerateResponse)(nil), // 1: jsx2html.v1.GenerateResponse
}
var file_jsx2html_v1_service_proto_depIdxs = []int32{
	0, // 0: jsx2html.v1.Jsx2HtmlService.Generate:input_type -> jsx2html.v1.GenerateRequest
	1, // 1: jsx2html.v1.Jsx2HtmlService.Generate:output_type -> jsx2html.v1.GenerateResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_jsx2html_v1_service_proto_init() }
func file_jsx2html_v1_service_proto_init() {
	if File_jsx2html_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_jsx2html_v1_service_proto_rawDesc), len(file_jsx2html_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_jsx2html_v1_service_proto_goTypes,
		DependencyIndexes: file_jsx2html_v1_service_proto_depIdxs,
	}.Build()
	File_jsx2html_v1_service_proto = out.File
	file_jsx2html_v1_service_proto_goTypes = nil
	file_jsx2html_v1_service_proto_depIdxs = nil
}
