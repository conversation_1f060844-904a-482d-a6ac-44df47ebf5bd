{{/*
  RenderPrintDoacoesRecebidas.tmpl
  Expects struct:
  type DoacoesRecebidasSection struct {
    Donations []struct {
      Donor       string
      Amount      string
      Date        string
      Description string
      // Add other fields as needed
    }
  }
*/}}

<div class="doacoes-recebidas-section">
  <h2 class="section-header">DOAÇÕES RECEBIDAS</h2>
  {{if .Donations}}
    <table class="doacoes-recebidas-table">
      <thead>
        <tr>
          <th>Doador</th>
          <th>Valor</th>
          <th>Data</th>
          <th>Descrição</th>
        </tr>
      </thead>
      <tbody>
        {{range .Donations}}
        <tr>
          <td>{{.Donor}}</td>
          <td>{{.Amount}}</td>
          <td>{{.Date}}</td>
          <td>{{.Description}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhuma doação recebida encontrada.</div>
  {{end}}
</div> 