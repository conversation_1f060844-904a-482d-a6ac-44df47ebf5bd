import React from 'react';
import {
  Document,
  Page,
  View,
  Text,
  StyleSheet,
  Image,
  Svg,
  Rect,
} from '@react-pdf/renderer';
import path from 'path';
import { getImageAsDataUrl } from '../helpers/index';
import { ReportType } from '../types/global';
import { usePrintStrategyMap } from '../strategies/printStrategyFactory';
import { PrintProfileHeader } from './PrintProfileHeader';
import { PrintSummary } from './PrintSummary';
import { ReportMetadata, ReportSection } from '../proto/jsx2html/v1/model_pb.js';

type ReportSearchArgs = Record<string, string>;

export interface ReportDocumentProps {
  sections: ReportSection[];
  metadata: ReportMetadata;
}

export const ReportDocument: React.FC<ReportDocumentProps> = ({
  sections,
  metadata,
}) => {

  const title = metadata.reportName || "Relatório";
  const reportType = (metadata.reportType as ReportType) || '';
  const reportSearchArgs = metadata.reportSearchArgs as ReportSearchArgs || {};

  const printMap = usePrintStrategyMap(reportType);
  const printableSections = Array.isArray(sections)
    ? sections.filter(
      (section) =>
        typeof section.title === 'string' &&
        !section.subsection &&
        printMap[section.title as keyof typeof printMap]
    )
    : [];

  const searchValue = Object.values(reportSearchArgs)[0] || '';

  // Converter imagens para base64 para renderização confiável
  const assetsPath = path.join(__dirname, '../assets');
  const iconSrc = getImageAsDataUrl(path.join(assetsPath, `print-${reportType}.png`));
  const logoSrc = getImageAsDataUrl(path.join(assetsPath, 'pwa-192x192.png'));
  const grafismoSrc = getImageAsDataUrl(path.join(assetsPath, 'grafismo.png'));
  const grafismoBrancoSrc = getImageAsDataUrl(path.join(assetsPath, 'grafismo_branco.png'));

  const summaryItems = printableSections.map(section => ({
    title: section.title,
    data_count: section.dataCount || 0
  }));

  const sectionsPerPage = 3; //max seção por página
  const pages = [];
  for (let i = 0; i < printableSections.length; i += sectionsPerPage) {
    pages.push(printableSections.slice(i, i + sectionsPerPage));
  }

  const renderHeader =
    <View style={styles.header} fixed>
      <View style={styles.logoContainer}>
        {logoSrc && <Image src={logoSrc} style={styles.logo} />}
      </View>

      <View style={styles.headerContent}>
        <Text style={styles.title}>{title.toUpperCase()}</Text>
        <View style={styles.reportTypeContainer}>
          {['cpf', 'telefone', 'email', 'cnpj'].includes(reportType) && iconSrc ? (
            <Image src={iconSrc} style={styles.searchIcon} />
          ) : (
            <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
              <Rect width="8" height="8" fill="#FE473C" />
            </Svg>
          )}
          <Text style={styles.searchValue}>{searchValue}</Text>
        </View>
      </View>

      {grafismoSrc && <Image src={grafismoSrc} style={styles.spiralImage} />}
    </View>

  const renderFooter =
    <View style={styles.footer} fixed>
      {grafismoBrancoSrc && (
        <Image
          src={grafismoBrancoSrc}
          style={styles.footerSpiralImage}
        />
      )}
      <View style={styles.footerContent}>
        <Text
          style={styles.pageNumber}
          render={({ pageNumber, totalPages }) =>
            `Página ${pageNumber
              ?.toString()
              ?.padStart(2, '0')} de ${totalPages
                ?.toString()
                ?.padStart(2, '0')}`
          }
        />
      </View>
    </View>

  return (
    <Document>
      {/* Primeira página */}
      <Page size="A4" style={styles.page}>
        {renderHeader}
        <View style={styles.customHeader}>
          <PrintProfileHeader metadata={metadata} report_type={reportType} searchValue={searchValue} />
        </View>
        <PrintSummary items={summaryItems} />
        {renderFooter}
      </Page>

      {/* Páginas seções */}
      {pages?.map((pageSections, pIdx) => (
        <Page key={pIdx} size="A4" style={styles.page}>
          {renderHeader}
          {pageSections.map((section, sectionIdx) => {
            const Renderer = printMap[section.title as keyof typeof printMap];
            if (typeof Renderer === 'function') {
              return <View key={`${pIdx}-${sectionIdx}`}>{Renderer(section as any)}</View>;
            }
            return null;
          })}
          {renderFooter}
        </Page>
      ))}
    </Document>
  );
};

const styles = StyleSheet.create({
  page: {
    fontFamily: 'Helvetica',
    fontSize: 12,
    paddingTop: 80,
    paddingBottom: 90,
    paddingHorizontal: 20,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 70,
    backgroundColor: '#E5E5EA',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    overflow: 'hidden',
  },
  logoContainer: {
    paddingTop: 2,
    marginRight: 20
  },
  logo: {
    width: 24,
    height: 'auto'
  },
  headerContent: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center'
  },
  title: {
    fontSize: 16
  },
  reportTypeContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  searchIcon: {
    width: 10,
    height: 10,
    marginRight: 4
  },
  searchValue: {
    fontSize: 10
  },
  spiralImage: {
    position: 'absolute',
    right: -110,
    top: -60,
    width: 300,
    height: 'auto',
    objectFit: 'contain',
    objectPosition: 'bottom right',
  },
  customHeader: {
    paddingVertical: 10,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: "#889EA3",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    overflow: "hidden",
  },
  footerSpiralImage: {
    position: 'absolute',
    left: -120,
    bottom: -148,
    width: 350,
    height: 'auto',
    objectFit: 'contain',
    objectPosition: 'bottom right',
  },
  footerContent: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  pageNumber: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'normal',
  },
});
