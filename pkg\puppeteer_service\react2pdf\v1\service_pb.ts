// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file react2pdf/v1/service.proto (package react2pdf.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { GenerateRequestSchema, GenerateResponseSchema } from "./model_pb";
import { file_react2pdf_v1_model } from "./model_pb";

/**
 * Describes the file react2pdf/v1/service.proto.
 */
export const file_react2pdf_v1_service: GenFile = /*@__PURE__*/
  fileDesc("ChpyZWFjdDJwZGYvdjEvc2VydmljZS5wcm90bxIMcmVhY3QycGRmLnYxMl8KEFJlYWN0MlBkZlNlcnZpY2USSwoIR2VuZXJhdGUSHS5yZWFjdDJwZGYudjEuR2VuZXJhdGVSZXF1ZXN0Gh4ucmVhY3QycGRmLnYxLkdlbmVyYXRlUmVzcG9uc2UwAUIvWi1wcmludGluZy1zZXJ2aWNlL3BrZy9yZWFjdDJwZGYvdjE7cmVhY3QycGRmdjFiBnByb3RvMw", [file_react2pdf_v1_model]);

/**
 * @generated from service react2pdf.v1.React2PdfService
 */
export const React2PdfService: GenService<{
  /**
   * @generated from rpc react2pdf.v1.React2PdfService.Generate
   */
  generate: {
    methodKind: "server_streaming";
    input: typeof GenerateRequestSchema;
    output: typeof GenerateResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_react2pdf_v1_service, 0);

