package services

import (
	"context"
	"fmt"
	"net/http"
	"os"

	"connectrpc.com/connect"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/types/known/structpb"

	domain "snap-printer/pkg/printing_service/domain/reports/pdf"
	jsx2htmlv1 "snap-printer/pkg/printing_service/proto/jsx2html/v1"
	"snap-printer/pkg/printing_service/proto/jsx2html/v1/jsx2htmlv1connect"
)

var (
	jsx2htmlAddress = "http://jsx2html:3002"
	jsx2htmlClient  jsx2htmlv1connect.Jsx2HtmlServiceClient
)

func init() {
	if addr := os.Getenv("JSX2HTML_SERVICE_URL"); addr != "" {
		jsx2htmlAddress = addr
	}
	log.Info().Str("JSX2HTML_SERVICE_URL", jsx2htmlAddress).Msg("Initializing Jsx2Html service client")
	jsx2htmlClient = jsx2htmlv1connect.NewJsx2HtmlServiceClient(
		http.DefaultClient,
		jsx2htmlAddress,
	)
}

func Jsx2Html(reportData domain.ReportData) (string, error) {
	req := &jsx2htmlv1.GenerateRequest{
		Metadata: &jsx2htmlv1.ReportMetadata{},
		Sections: make([]*jsx2htmlv1.ReportSection, len(reportData.Sections)),
	}

	// Map Metadata
	if reportData.Metadata.ReportSearchArgs != nil {
		searchArgs, err := structpb.NewStruct(reportData.Metadata.ReportSearchArgs)
		if err != nil {
			return "", fmt.Errorf("failed to convert search args to structpb: %w", err)
		}
		req.Metadata.ReportSearchArgs = searchArgs
	}
	req.Metadata.ReportId = reportData.Metadata.ReportID
	req.Metadata.ReportStatus = reportData.Metadata.ReportStatus
	req.Metadata.ReportType = reportData.Metadata.ReportType
	req.Metadata.ReportName = reportData.Metadata.ReportName
	req.Metadata.CreationAt = reportData.Metadata.CreationAt
	req.Metadata.ModifiedAt = reportData.Metadata.ModifiedAt
	req.Metadata.SubjectName = reportData.Metadata.SubjectName
	req.Metadata.SubjectMotherName = reportData.Metadata.SubjectMotherName
	req.Metadata.SubjectAge = int32(reportData.Metadata.SubjectAge)
	req.Metadata.SubjectSex = reportData.Metadata.SubjectSex

	// Map Sections
	for i, s := range reportData.Sections {
		sectionData := make([]*structpb.Struct, len(s.Data))
		for j, d := range s.Data {
			structData, err := structpb.NewStruct(d)
			if err != nil {
				return "", fmt.Errorf("failed to convert section data to structpb: %w", err)
			}
			sectionData[j] = structData
		}

		req.Sections[i] = &jsx2htmlv1.ReportSection{
			Title:      s.Title,
			Subtitle:   s.Subtitle,
			Subsection: s.Subsection,
			IsDeleted:  s.IsDeleted,
			Source:     s.Source,
			DataCount:  int32(s.DataCount),
			Data:       sectionData,
		}
	}

	res, err := jsx2htmlClient.Generate(context.Background(), connect.NewRequest(req))
	if err != nil {
		return "", fmt.Errorf("failed to call jsx2html service: %w", err)
	}

	return res.Msg.Html, nil
}
