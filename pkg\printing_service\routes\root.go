package routes

import (
	"snap-printer/pkg/printing_service/io"
	pdfroutes "snap-printer/pkg/printing_service/routes/reports/pdf"

	"github.com/gin-gonic/gin"
)

// SetupRoutes sets up the HTTP routes for the application.
func SetupRoutes(r *gin.Engine) {
	r.GET("/", io.Root)
	r.GET("/health", io.Root) // Same handler for health checks

	// PDF generation routes
	pdfroutes.SetupRoutes(r)
	pdfroutes.SetupJSX2PDFRoutes(r)
}
