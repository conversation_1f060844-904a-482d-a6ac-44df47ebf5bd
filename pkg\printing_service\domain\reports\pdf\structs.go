// domain/reports/pdf/structs.go
package domain

// ReportData represents the structure of the incoming JSON data for PDF reports.
type ReportData struct {
	Metadata Metadata `json:"metadata"`
	Sections []Section `json:"sections"`
}

// Metadata represents the metadata for the report.
type Metadata struct {
	ReportID          string                 `json:"report_id"`
	ReportStatus      string                 `json:"report_status"`
	ReportType        string                 `json:"report_type"`
	ReportSearchArgs  map[string]interface{} `json:"report_search_args"`
	ReportName        string                 `json:"report_name"`
	CreationAt        string                 `json:"creation_at"`
	ModifiedAt        string                 `json:"modified_at"`
	SubjectName       string                 `json:"subject_name"`
	SubjectMotherName string                 `json:"subject_mother_name"`
	SubjectAge        int                    `json:"subject_age"`
	SubjectSex        string                 `json:"subject_sex"`
}

// Section represents a section within the report.
type Section struct {
	Title      string           `json:"title"`
	Subtitle   string           `json:"subtitle"`
	Subsection string           `json:"subsection"`
	IsDeleted  bool             `json:"is_deleted"`
	Source     []string         `json:"source"`
	DataCount  int              `json:"data_count"`
	Data       []map[string]any `json:"data"`
}