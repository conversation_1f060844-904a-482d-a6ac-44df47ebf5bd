// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file html2pdf/v1/model.proto (package html2pdf.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file html2pdf/v1/model.proto.
 */
export const file_html2pdf_v1_model: GenFile = /*@__PURE__*/
  fileDesc("ChdodG1sMnBkZi92MS9tb2RlbC5wcm90bxILaHRtbDJwZGYudjEiHwoPR2VuZXJhdGVSZXF1ZXN0EgwKBGh0bWwYASABKAkiIQoQR2VuZXJhdGVSZXNwb25zZRINCgVjaHVuaxgBIAEoDEIpWidzbmFwLXByaW50ZXIvcGtnL2h0bWwycGRmL3YxO2h0bWwycGRmdjFiBnByb3RvMw");

/**
 * @generated from message html2pdf.v1.GenerateRequest
 */
export type GenerateRequest = Message<"html2pdf.v1.GenerateRequest"> & {
  /**
   * @generated from field: string html = 1;
   */
  html: string;
};

/**
 * Describes the message html2pdf.v1.GenerateRequest.
 * Use `create(GenerateRequestSchema)` to create a new message.
 */
export const GenerateRequestSchema: GenMessage<GenerateRequest> = /*@__PURE__*/
  messageDesc(file_html2pdf_v1_model, 0);

/**
 * @generated from message html2pdf.v1.GenerateResponse
 */
export type GenerateResponse = Message<"html2pdf.v1.GenerateResponse"> & {
  /**
   * @generated from field: bytes chunk = 1;
   */
  chunk: Uint8Array;
};

/**
 * Describes the message html2pdf.v1.GenerateResponse.
 * Use `create(GenerateResponseSchema)` to create a new message.
 */
export const GenerateResponseSchema: GenMessage<GenerateResponse> = /*@__PURE__*/
  messageDesc(file_html2pdf_v1_model, 1);

