<!DOCTYPE html>
<html>
<head>
	<title>{{if .Metadata.ReportName}}{{.Metadata.ReportName}}{{else}}Relatório{{end}}</title>
 <style>
  /* Base styles */
  body {
  	font-family: Helvetica, Arial, sans-serif;
  	font-size: 12px;
  	margin: 0;
  	padding: 0;
  	color: #333;
  	counter-reset: page;
  }

  /* Document structure */
  .document {
  	width: 100%;
  }

  .page {
  	position: relative;
  	padding-top: 80px;
  	padding-bottom: 90px;
  	padding-left: 20px;
  	padding-right: 20px;
  	page-break-after: always;
  	counter-increment: page;
  }

  /* Header styles */
  .header {
  	position: fixed;
  	top: 0;
  	left: 0;
  	right: 0;
  	height: 70px;
  	background-color: #E5E5EA;
  	display: flex;
  	flex-direction: row;
  	align-items: center;
  	padding: 16px 20px;
  	overflow: hidden;
  	z-index: 100;
  }

  .logo-container {
  	padding-top: 2px;
  	margin-right: 20px;
  }

  .logo {
  	width: 24px;
  	height: auto;
  }

  .header-content {
  	flex: 1;
  	display: flex;
  	flex-direction: column;
  	justify-content: center;
  }

  .title {
  	font-size: 16px;
  	margin: 0;
  	padding: 0;
  }

  .report-type-container {
  	display: flex;
  	flex-direction: row;
  	align-items: flex-start;
  }

  .search-icon {
  	width: 10px;
  	height: 10px;
  	margin-right: 4px;
  }

  .search-icon-fallback {
  	width: 8px;
  	height: 8px;
  	background-color: #FE473C;
  	margin-right: 4px;
  }

  .search-value {
  	font-size: 10px;
  }

  .spiral-image {
  	position: absolute;
  	right: -110px;
  	top: -60px;
  	width: 300px;
  	height: auto;
  	object-fit: contain;
  	object-position: bottom right;
  }

  /* Profile Header styles */
  .profile-header {
  	padding-top: 10px;
  	padding-bottom: 10px;
  	width: 100%;
  }

  .top-row {
  	display: flex;
  	flex-direction: row;
  	justify-content: space-between;
  	margin-bottom: 20px;
  }

  .profile-header-section {
  	width: 48%;
  	padding-right: 16px;
  }

  .entradas-section {
  	width: 48%;
  }

  .section-header {
  	font-size: 12px;
  	font-weight: bold;
  	color: #FE473C;
  	margin-bottom: 16px;
  	letter-spacing: 0.5px;
  	text-transform: uppercase;
  }

  .photo-container {
  	display: flex;
  	align-items: center;
  	justify-content: center;
  	margin-bottom: 20px;
  }

  .avatar-box {
  	width: 80px;
  	height: 80px;
  	border-radius: 40px;
  	background-color: #E5E7EB;
  	display: flex;
  	justify-content: center;
  	align-items: center;
  	overflow: hidden;
  }

  .avatar-fallback {
  	font-size: 24px;
  	font-weight: bold;
  	color: #6B7280;
  	text-align: center;
  }

  .data-list-container {
  	display: flex;
  	flex-direction: row;
  	justify-content: space-between;
  }

  .data-column {
  	width: 48%;
  }

  .data-row {
  	margin-bottom: 12px;
  	padding-bottom: 8px;
  	border-bottom: 1px solid #E5E7EB;
  }

  .data-label {
  	display: block;
  	font-size: 8px;
  	font-weight: bold;
  	color: #FE473C;
  	margin-bottom: 4px;
  	letter-spacing: 0.5px;
  	text-transform: uppercase;
  }

  .data-value {
  	font-size: 10px;
  	font-weight: normal;
  }

  .entry-container {
  	padding: 8px;
  	background-color: #F9F9FA;
  	border-radius: 4px;
  }

  .entry-box {
  	border-top: 1px solid #E5E7EB;
  	padding-top: 4px;
  	padding-bottom: 4px;
  }

  /* Summary styles */
  .summary-container {
  	width: 100%;
  	margin-bottom: 20px;
  }

  .registros-section {
  	background-color: #F9F9FA;
  	border-radius: 4px;
  	padding: 10px;
  }

  .registros-header {
  	font-size: 11px;
  	font-weight: bold;
  	color: #374151;
  	margin-bottom: 12px;
  	letter-spacing: 0.5px;
  }

  .total-number {
  	color: #FE473C;
  }

  .registros-list {
  	display: flex;
  	flex-direction: row;
  	justify-content: space-between;
  }

  .column {
  	width: 48%;
  }

  .registro-item {
  	display: flex;
  	flex-direction: row;
  	justify-content: space-between;
  	align-items: center;
  	margin-top: 4px;
  	margin-bottom: 4px;
  }

  .registro-title {
  	font-size: 11px;
  	font-weight: bold;
  	color: #6B7280;
  	flex: 1;
  }

  .count-badge {
  	background-color: #6B7280;
  	border-radius: 12px;
  	padding: 3px 8px;
  	min-width: 32px;
  	text-align: center;
  }

  .count-text {
  	font-size: 9px;
  	font-weight: bold;
  	color: #FFFFFF;
  	text-align: center;
  }

  /* Section styles */
  .section-container {
  	margin-bottom: 16px;
  }

  .section-title-container {
  	display: flex;
  	flex-direction: row;
  	align-items: flex-start;
  	border-bottom: 1px solid #889EA3;
  }

  .section-icon {
  	width: 4px;
  	height: 4px;
  	background-color: #FE473C;
  	margin-right: 4px;
  	margin-top: 6px;
  }

  .section-heading {
  	font-size: 12px;
  	text-transform: uppercase;
  	margin-bottom: 8px;
  	font-weight: bold;
  }

  .section-grid {
  	padding-left: 8px;
  	padding-top: 8px;
  	display: flex;
  	flex-direction: row;
  	flex-wrap: wrap;
  	background-color: #F9F9FA;
  	border-bottom-left-radius: 2px;
  	border-bottom-right-radius: 2px;
  }

  .section-cell {
  	width: 50%;
  	padding-right: 8px;
  	margin-bottom: 8px;
  }

  .info-container {
  	display: flex;
  	flex-direction: row;
  	align-items: flex-start;
  	flex-wrap: wrap;
  }

  .info-icon {
  	width: 4px;
  	height: 4px;
  	background-color: #CCCCCC;
  	margin-right: 4px;
  	margin-top: 4px;
  }

  .info-label {
  	font-size: 10px;
  	font-weight: bold;
  	margin-bottom: 2px;
  	color: #889EA3;
  }

  .source-text {
  	padding-left: 4px;
  	padding-bottom: 2px;
  	font-size: 8px;
  	color: #FE473C;
  }

  .info-value {
  	display: block;
  	padding-left: 8px;
  	font-size: 10px;
  }

  /* Footer styles */
  .footer {
  	position: fixed;
  	bottom: 0;
  	left: 0;
  	right: 0;
  	height: 50px;
  	background-color: #889EA3;
  	display: flex;
  	flex-direction: row;
  	align-items: center;
  	padding: 0 20px;
  	overflow: hidden;
  	z-index: 100;
  }

  .footer-spiral-image {
  	position: absolute;
  	left: -120px;
  	bottom: -148px;
  	width: 350px;
  	height: auto;
  	object-fit: contain;
  	object-position: bottom right;
  }

  .footer-content {
  	flex: 1;
  	display: flex;
  	justify-content: flex-end;
  	align-items: flex-end;
  }

  .page-number::after {
  	font-size: 12px;
  	color: #FFFFFF;
  	font-weight: normal;
  	content: "Página " counter(page);
  }
 </style>
	<meta charset="UTF-8">
</head>
<body>
	{{$reportType := .Metadata.ReportType}}
	{{$searchValue := .Metadata.ReportSearchArgs.value.cpf}}

	<!-- Header -->
	<div class="header">
		<div class="logo-container">
   <img src="http://localhost:8080/templates/reports/assets/pwa-192x192.png" alt="Logo" class="logo">
		</div>
		<div class="header-content">
			<h1 class="title">{{if .Metadata.ReportName}}{{.Metadata.ReportName | toUpper}}{{else}}RELATÓRIO{{end}}</h1>
			<div class="report-type-container">
				{{if or (eq $reportType "cpf") (eq $reportType "telefone") (eq $reportType "email") (eq $reportType "cnpj")}}
     <img src="http://localhost:8080/templates/reports/assets/print-{{$reportType}}.png" alt="{{$reportType}}" class="search-icon">
				{{else}}
					<div class="search-icon-fallback"></div>
				{{end}}
				<span class="search-value">{{$searchValue}}</span>
			</div>
		</div>
  <img src="http://localhost:8080/templates/reports/assets/grafismo.png" alt="Grafismo" class="spiral-image">
	</div>

	<!-- Footer -->
	<div class="footer">
  <img src="http://localhost:8080/templates/reports/assets/grafismo_branco.png" alt="Grafismo Branco" class="footer-spiral-image">
		<div class="footer-content">
			<span class="page-number"></span>
		</div>
	</div>

	<div class="document">
		<!-- First page -->
		<div class="page">
			<!-- Profile Header -->
			<div class="profile-header">
				<div class="top-row">
					<div class="profile-header-section">
						<h2 class="section-header">PERFIL</h2>
						<div class="photo-container">
							<div class="avatar-box">
								<span class="avatar-fallback">{{getInitials .Metadata.SubjectName}}</span>
							</div>
						</div>
					</div>

					<div class="entradas-section">
						<h2 class="section-header">ENTRADAS</h2>
						<div class="entry-container">
							<span class="data-label">{{$reportType}}</span>
							<div class="entry-box">
								<span class="data-value">{{$searchValue}}</span>
							</div>
						</div>
					</div>
				</div>

				<div class="data-list-container">
					<div class="data-column">
						<div class="data-row">
							<span class="data-label">NOME</span>
							<span class="data-value">{{.Metadata.SubjectName}}</span>
						</div>
						<div class="data-row">
							<span class="data-label">NOME DA MÃE</span>
							<span class="data-value">{{if .Metadata.SubjectMotherName}}{{.Metadata.SubjectMotherName}}{{else}}N/A{{end}}</span>
						</div>
					</div>
					<div class="data-column">
						<div class="data-row">
							<span class="data-label">IDADE</span>
							<span class="data-value">{{if .Metadata.SubjectAge}}{{.Metadata.SubjectAge}}{{else}}N/A{{end}}</span>
						</div>
						<div class="data-row">
							<span class="data-label">SEXO</span>
							<span class="data-value">{{if .Metadata.SubjectSex}}{{.Metadata.SubjectSex}}{{else}}N/A{{end}}</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Summary -->
			<div class="summary-container">
				<div class="registros-section">
					{{$total := 0}}
					{{range .Sections}}
						{{if and (ne .Subsection "") (ne .IsDeleted true)}}
							{{$total = add $total .DataCount}}
						{{end}}
					{{end}}
					<h2 class="registros-header">
						<span class="total-number">{{$total}}</span> REGISTROS ENCONTRADOS
					</h2>

					<div class="registros-list">
						{{$sections := .Sections}}
						{{$midpoint := div (len $sections) 2}}

						<div class="column">
							{{range $index, $section := $sections}}
								{{if lt $index $midpoint}}
									{{if and (ne .Subsection "") (ne .IsDeleted true)}}
										<div class="registro-item">
											<span class="registro-title">{{.Title}}</span>
											<div class="count-badge">
												<span class="count-text">{{.DataCount}}</span>
											</div>
										</div>
									{{end}}
								{{end}}
							{{end}}
						</div>

						<div class="column">
							{{range $index, $section := $sections}}
								{{if ge $index $midpoint}}
									{{if and (ne .Subsection "") (ne .IsDeleted true)}}
										<div class="registro-item">
											<span class="registro-title">{{.Title}}</span>
											<div class="count-badge">
												<span class="count-text">{{.DataCount}}</span>
											</div>
										</div>
									{{end}}
								{{end}}
							{{end}}
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Section pages -->
		{{.SectionsHTML}}
	</div>
</body>
</html>

{{define "section"}}
{{if and (ne .Title "") (ne .IsDeleted true)}}
    <div class="section-container">
        <div class="section-title-container">
            <div class="section-icon"></div>
            <h2 class="section-heading">{{.Title}}</h2>
        </div>

        <div class="section-grid">
            {{range .Data}}
                {{if .detalhes}}
                    {{range $key, $value := .detalhes}}
                            {{if and (ne $value.IsDeleted true) ($value.value)}}
                            <div class="section-cell">
                                <div class="info-container">
                                    <div class="info-icon"></div>
                                    <span class="info-label">{{$value.label | toUpper}}</span>
                                    <span class="source-text">
                                        {{range $source := $value.source}}
                                            | {{$source}} 
                                        {{end}}
                                    </span>
                                </div>
                                <span class="info-value">{{$value.value}}</span>
                            </div>
                        {{end}}
                    {{end}}
                {{else}}
                    {{range $key, $value := .}}
                            {{if not (eq $key "IsDeleted")}}
                            <div class="section-cell">
                                <div class="info-container">
                                    <div class="info-icon"></div>
                                    <span class="info-label">{{$key | toUpper}}</span>
                                </div>
                                <span class="info-value">{{$value}}</span>
                            </div>
                        {{end}}
                    {{end}}
                {{end}}
            {{end}}
        </div>
    </div>
{{end}}
{{end}}

{{define "page"}}
<div class="page">
    {{range .}}
        {{template "section" .}}
    {{end}}
</div>
{{end}}
