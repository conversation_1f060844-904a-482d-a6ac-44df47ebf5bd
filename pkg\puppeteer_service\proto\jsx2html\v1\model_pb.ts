// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file jsx2html/v1/model.proto (package jsx2html.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file jsx2html/v1/model.proto.
 */
export const file_jsx2html_v1_model: GenFile = /*@__PURE__*/
  fileDesc("Chdqc3gyaHRtbC92MS9tb2RlbC5wcm90bxILanN4Mmh0bWwudjEibgoPR2VuZXJhdGVSZXF1ZXN0Ei0KCG1ldGFkYXRhGAEgASgLMhsuanN4Mmh0bWwudjEuUmVwb3J0TWV0YWRhdGESLAoIc2VjdGlvbnMYAiADKAsyGi5qc3gyaHRtbC52MS5SZXBvcnRTZWN0aW9uIr8DCg5SZXBvcnRNZXRhZGF0YRIcCglyZXBvcnRfaWQYASABKAlSCXJlcG9ydF9pZBIkCg1yZXBvcnRfc3RhdHVzGAIgASgJUg1yZXBvcnRfc3RhdHVzEiAKC3JlcG9ydF90eXBlGAMgASgJUgtyZXBvcnRfdHlwZRJHChJyZXBvcnRfc2VhcmNoX2FyZ3MYBCABKAsyFy5nb29nbGUucHJvdG9idWYuU3RydWN0UhJyZXBvcnRfc2VhcmNoX2FyZ3MSIAoLcmVwb3J0X25hbWUYBSABKAlSC3JlcG9ydF9uYW1lEiAKC2NyZWF0aW9uX2F0GAYgASgJUgtjcmVhdGlvbl9hdBIgCgttb2RpZmllZF9hdBgHIAEoCVILbW9kaWZpZWRfYXQSIgoMc3ViamVjdF9uYW1lGAggASgJUgxzdWJqZWN0X25hbWUSMAoTc3ViamVjdF9tb3RoZXJfbmFtZRgJIAEoCVITc3ViamVjdF9tb3RoZXJfbmFtZRIgCgtzdWJqZWN0X2FnZRgKIAEoBVILc3ViamVjdF9hZ2USIAoLc3ViamVjdF9zZXgYCyABKAlSC3N1YmplY3Rfc2V4IrsBCg1SZXBvcnRTZWN0aW9uEg0KBXRpdGxlGAEgASgJEhAKCHN1YnRpdGxlGAIgASgJEhIKCnN1YnNlY3Rpb24YAyABKAkSDgoGc291cmNlGAQgAygJEh4KCmRhdGFfY291bnQYBSABKAVSCmRhdGFfY291bnQSHgoKaXNfZGVsZXRlZBgGIAEoCFIKaXNfZGVsZXRlZBIlCgRkYXRhGAcgAygLMhcuZ29vZ2xlLnByb3RvYnVmLlN0cnVjdCIgChBHZW5lcmF0ZVJlc3BvbnNlEgwKBGh0bWwYASABKAlCQFo+c25hcC1wcmludGVyL3BrZy9wcmludGluZ19zZXJ2aWNlL3Byb3RvL2pzeDJodG1sL3YxO2pzeDJodG1sdjFiBnByb3RvMw", [file_google_protobuf_struct]);

/**
 * @generated from message jsx2html.v1.GenerateRequest
 */
export type GenerateRequest = Message<"jsx2html.v1.GenerateRequest"> & {
  /**
   * @generated from field: jsx2html.v1.ReportMetadata metadata = 1;
   */
  metadata?: ReportMetadata;

  /**
   * @generated from field: repeated jsx2html.v1.ReportSection sections = 2;
   */
  sections: ReportSection[];
};

/**
 * Describes the message jsx2html.v1.GenerateRequest.
 * Use `create(GenerateRequestSchema)` to create a new message.
 */
export const GenerateRequestSchema: GenMessage<GenerateRequest> = /*@__PURE__*/
  messageDesc(file_jsx2html_v1_model, 0);

/**
 * @generated from message jsx2html.v1.ReportMetadata
 */
export type ReportMetadata = Message<"jsx2html.v1.ReportMetadata"> & {
  /**
   * @generated from field: string report_id = 1 [json_name = "report_id"];
   */
  reportId: string;

  /**
   * @generated from field: string report_status = 2 [json_name = "report_status"];
   */
  reportStatus: string;

  /**
   * @generated from field: string report_type = 3 [json_name = "report_type"];
   */
  reportType: string;

  /**
   * @generated from field: google.protobuf.Struct report_search_args = 4 [json_name = "report_search_args"];
   */
  reportSearchArgs?: JsonObject;

  /**
   * @generated from field: string report_name = 5 [json_name = "report_name"];
   */
  reportName: string;

  /**
   * @generated from field: string creation_at = 6 [json_name = "creation_at"];
   */
  creationAt: string;

  /**
   * @generated from field: string modified_at = 7 [json_name = "modified_at"];
   */
  modifiedAt: string;

  /**
   * @generated from field: string subject_name = 8 [json_name = "subject_name"];
   */
  subjectName: string;

  /**
   * @generated from field: string subject_mother_name = 9 [json_name = "subject_mother_name"];
   */
  subjectMotherName: string;

  /**
   * @generated from field: int32 subject_age = 10 [json_name = "subject_age"];
   */
  subjectAge: number;

  /**
   * @generated from field: string subject_sex = 11 [json_name = "subject_sex"];
   */
  subjectSex: string;
};

/**
 * Describes the message jsx2html.v1.ReportMetadata.
 * Use `create(ReportMetadataSchema)` to create a new message.
 */
export const ReportMetadataSchema: GenMessage<ReportMetadata> = /*@__PURE__*/
  messageDesc(file_jsx2html_v1_model, 1);

/**
 * @generated from message jsx2html.v1.ReportSection
 */
export type ReportSection = Message<"jsx2html.v1.ReportSection"> & {
  /**
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: string subtitle = 2;
   */
  subtitle: string;

  /**
   * @generated from field: string subsection = 3;
   */
  subsection: string;

  /**
   * @generated from field: repeated string source = 4;
   */
  source: string[];

  /**
   * @generated from field: int32 data_count = 5 [json_name = "data_count"];
   */
  dataCount: number;

  /**
   * @generated from field: bool is_deleted = 6 [json_name = "is_deleted"];
   */
  isDeleted: boolean;

  /**
   * @generated from field: repeated google.protobuf.Struct data = 7;
   */
  data: JsonObject[];
};

/**
 * Describes the message jsx2html.v1.ReportSection.
 * Use `create(ReportSectionSchema)` to create a new message.
 */
export const ReportSectionSchema: GenMessage<ReportSection> = /*@__PURE__*/
  messageDesc(file_jsx2html_v1_model, 2);

/**
 * @generated from message jsx2html.v1.GenerateResponse
 */
export type GenerateResponse = Message<"jsx2html.v1.GenerateResponse"> & {
  /**
   * @generated from field: string html = 1;
   */
  html: string;
};

/**
 * Describes the message jsx2html.v1.GenerateResponse.
 * Use `create(GenerateResponseSchema)` to create a new message.
 */
export const GenerateResponseSchema: GenMessage<GenerateResponse> = /*@__PURE__*/
  messageDesc(file_jsx2html_v1_model, 3);

