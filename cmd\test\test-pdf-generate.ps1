# Test script for the new PDF generation endpoint
# This script tests the /pdf/generate endpoint that mirrors the TypeScript service

Write-Host "Testing PDF Generation Endpoint..." -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

# Set the server URL (adjust if needed)
$ServerUrl = "http://localhost:8080"
# $ServerUrl = "http://localhost:1127"

# Check if server is running
Write-Host "Checking if server is running..."
try {
    $response = Invoke-WebRequest -Uri "$ServerUrl/" -Method GET -TimeoutSec 5
    Write-Host "Server is running!" -ForegroundColor Green
}
catch {
    Write-Host "Error: Server is not running at $ServerUrl" -ForegroundColor Red
    Write-Host "Please start the server first with: go run ." -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Test the new PDF generation endpoint
Write-Host "Testing /reports/pdf/generate..." -ForegroundColor Cyan
Write-Host "Sending request with test payload..."

$payloadPath = "./generate-pdf-request.json"
# $payloadPath = "./reports-pdf-data.json"
$contentType = "application/json"
Write-Host "Using generate-pdf-request format with $payloadPath" -ForegroundColor Cyan

if (-not (Test-Path $payloadPath)) {
    Write-Host "Error: Test payload file not found: $payloadPath" -ForegroundColor Red
    exit 1
}

# Start timing for parsing input
$parseInputStopwatch = [System.Diagnostics.Stopwatch]::StartNew()
$payload = Get-Content $payloadPath -Raw
$parseInputStopwatch.Stop()
$parseInputTime = $parseInputStopwatch.Elapsed.TotalSeconds
Write-Host "Parse input time: $($parseInputTime)s" -ForegroundColor Yellow

# Set headers
$headers = @{
    "Content-Type" = $contentType
    #     "Authorization" = "Bearer your-access-key-here"
}

try {
    # Start timing for overall process
    $totalStopwatch = [System.Diagnostics.Stopwatch]::StartNew()

    # Make the request (this includes PDF creation time)
    $pdfCreateStopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    $response = Invoke-WebRequest -Uri "$ServerUrl/pdf/reports/jsx2pdf" -Method POST -Body $payload -Headers $headers -OutFile "test-output.pdf"
    $pdfCreateStopwatch.Stop()
    $pdfCreateTime = $pdfCreateStopwatch.Elapsed.TotalSeconds

    # Stop timing for overall process
    $totalStopwatch.Stop()
    $totalTime = $totalStopwatch.Elapsed.TotalSeconds

    Write-Host "HTTP Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Parse input time: $($parseInputTime)s" -ForegroundColor Green
    Write-Host "Create PDF time: $($pdfCreateTime)s" -ForegroundColor Green
    Write-Host "Total time: $($totalTime)s" -ForegroundColor Green

    # Check if PDF was created
    if (Test-Path "test-output.pdf") {
        $fileInfo = Get-Item "test-output.pdf"
        Write-Host ""
        Write-Host "Success! PDF generated and saved as test-output.pdf" -ForegroundColor Green
        Write-Host "File size: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Green
    }
    else {
        Write-Host ""
        Write-Host "Error: PDF file was not created" -ForegroundColor Red
    }

}
catch {
    Write-Host ""
    Write-Host "Error making request: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green
