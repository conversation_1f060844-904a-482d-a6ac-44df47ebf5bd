{{/*
  RenderPrintFiliacaoPartidaria.tmpl
  Expects struct:
  type FiliacaoPartidariaSection struct {
    Affiliations []struct {
      Party     string
      StartDate string
      EndDate   string
      Status    string
      // Add other fields as needed
    }
  }
*/}}

<div class="filiacao-partidaria-section">
  <h2 class="section-header">FILIAÇÃO PARTIDÁRIA</h2>
  {{if .Affiliations}}
    <table class="filiacao-partidaria-table">
      <thead>
        <tr>
          <th>Partido</th>
          <th>Início</th>
          <th>Fim</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {{range .Affiliations}}
        <tr>
          <td>{{.Party}}</td>
          <td>{{.StartDate}}</td>
          <td>{{.EndDate}}</td>
          <td>{{.Status}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhuma filiação encontrada.</div>
  {{end}}
</div> 