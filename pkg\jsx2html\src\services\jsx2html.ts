import { ServiceImpl } from "@connectrpc/connect";
import { Jsx2HtmlService } from "../proto/jsx2html/v1/service_pb.js";
import { GenerateRequest, GenerateResponse, GenerateResponseSchema } from "../proto/jsx2html/v1/model_pb.js";
import { ReportDocument, ReportDocumentProps } from '../components/ReportDocument.js';
import { renderToStaticMarkup } from 'react-dom/server';
import { create } from "@bufbuild/protobuf";
import React from 'react';

export const Jsx2HtmlServiceImpl: ServiceImpl<typeof Jsx2HtmlService> = {
    async generate(req: GenerateRequest): Promise<GenerateResponse> {
        if (!req.sections || !Array.isArray(req.sections) || req.sections.length === 0) {
            throw new Error("Invalid sections provided in the request.");
        }
        if (!req.metadata) {
            throw new Error("Metadata is required in the request.");
        }

        const props: ReportDocumentProps = {
            sections: req.sections,
            metadata: req.metadata,
        };

        const html = renderToStaticMarkup(
            React.createElement(ReportDocument, props)
        );

        return create(GenerateResponseSchema, {
            html: html,
        });
    },
};
