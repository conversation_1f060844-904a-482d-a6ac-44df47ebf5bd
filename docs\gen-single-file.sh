#!/bin/bash
# filepath: get-single-file.sh

EXTENSION="tmpl"

# Output file
OUTPUT="all_project_files_$EXTENSION.txt"
> "$OUTPUT" # Clear output file

# Iterate over all extension files (excluding the output file itself)
find . -type f -name "*.$EXTENSION" ! -name "$(basename "$OUTPUT")" | while read -r file; do
    echo "===== START OF FILE: $file =====" >> "$OUTPUT"
    cat "$file" >> "$OUTPUT"
    echo -e "\n===== END OF FILE: $file =====\n" >> "$OUTPUT"
done

echo "All .$EXTENSION files concatenated"