import { TRANSLATED_LABELS, SOURCE_LABELS, PARSED_VALUES } from '../config/constants';
import path from 'path';
import fs from 'fs';

/**
 * Translates a property name to a user-friendly label
 */
export const translatePropToLabel = (propName: string): string => {
  if (!propName || typeof propName !== "string") return String(propName);

  const normalizedInput = propName.toLowerCase();

  // Create a lowercase map of keys for case-insensitive matching
  for (const key in TRANSLATED_LABELS) {
    if (key.toLowerCase() === normalizedInput) {
      return TRANSLATED_LABELS[key as keyof typeof TRANSLATED_LABELS];
    }
  }

  // Return original string if no match found
  return propName;
};

/**
 * Translates source names to user-friendly labels
 */
export const translateSource = (source: string): string => {
  if (!source || typeof source !== "string") return String(source);
  return SOURCE_LABELS[source] || source;
};

/**
 * Gets initials from a name
 */
export const getInitials = (name: string | undefined): string => {
  if (!name) return "N/A";

  const nameParts = name.trim().split(" ").filter(Boolean);

  if (nameParts.length === 1) {
    return nameParts[0][0].toUpperCase();
  } else if (nameParts.length > 1) {
    const firstInitial = nameParts[0][0];
    const lastInitial = nameParts[nameParts.length - 1][0];
    return `${firstInitial}${lastInitial}`.toUpperCase();
  }

  return "N/A";
};

/**
 * Gets the appropriate label for a field based on its structure
 */
export const getFieldLabel = (key: string, value: any): string => {
  // if the value has its own .label, translate _that_; otherwise translate the raw key
  const raw = typeof value === 'object' && value?.label
    ? value.label
    : key;

  return translatePropToLabel(raw);
};

/**
 * Gets the actual value from a field that might be a nested object
 */
export const getFieldValue = (value: any): any => {
  return typeof value === 'object' && value?.value !== undefined
    ? value.value
    : value;
};

/**
 * Checks if a string is a valid URL
 */
export const isValidUrl = (url: string | undefined | null) => {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Checks if a string is base64 image data
 */
export const isBase64Image = (str: string) => {
  if (!str) return false;
  try {
    if (str.startsWith('data:image')) {
      return true;
    }

    // Check for raw base64 image data by looking for common image format signatures
    if (str.startsWith('/9j/') || str.startsWith('/9k/') ||
      str.startsWith('iVBORw0KGgo') ||
      str.startsWith('R0lGODlh') || str.startsWith('R0lGODdh') ||
      str.startsWith('UklGR')) {
      return true;
    }

    // Fallback: try to validate as base64 (but only for shorter strings)
    if (str.length < 10000) {
      return btoa(atob(str)) == str;
    }

    return false;
  } catch (err) {
    return false;
  }
};

/**
 * Formats image source for display
 */
export const formatImageSrc = (value: string): string => {
  if (!value) return value;

  // If it's already a valid URL or data URL, return as is
  if (isValidUrl(value) || value.startsWith('data:')) {
    return value;
  }

  // If it's raw base64 data, add the appropriate data URL prefix
  if (isBase64Image(value)) {
    // Detect image type based on base64 signature
    if (value.startsWith('/9j/') || value.startsWith('/9k/')) {
      return `data:image/jpeg;base64,${value}`;
    } else if (value.startsWith('iVBORw0KGgo')) {
      return `data:image/png;base64,${value}`;
    } else if (value.startsWith('R0lGODlh') || value.startsWith('R0lGODdh')) {
      return `data:image/gif;base64,${value}`;
    } else if (value.startsWith('UklGR')) {
      return `data:image/webp;base64,${value}`;
    } else {
      // Default to JPEG if we can't determine the type
      return `data:image/jpeg;base64,${value}`;
    }
  }

  return value;
};

/**
 * Gets singular form of a word
 */
export const getSingular = (key: string): string => {
  if (!key) return "";

  const singularMap: Record<string, string> = {
    aplicativos: "aplicativo",
    enderecos: "endereço",
    veiculos: "veículo",
    diarios: "diário",
    oficiais: "oficial",
    sources: "fonte",
    socios: "sócio",
    movimentacoes: "movimentação",
    emails: "email",
    telefones: "telefone",
    pessoas: "pessoa",
    empresas: "empresa",
    processos: "processo",
  };

  const normalizedKey = key.toLowerCase();
  return singularMap[normalizedKey] || key;
};

/**
 * Gets plural form of a word
 */
export const getPlural = (key: string): string => {
  if (!key) return "";

  const pluralMap: Record<string, string> = {
    aplicativo: "aplicativos",
    endereco: "endereços",
    veiculo: "veículos",
    diario: "diários",
    oficial: "oficiais",
    fonte: "fontes",
    socio: "sócios",
    movimentacao: "movimentações",
    email: "emails",
    telefone: "telefones",
    pessoa: "pessoas",
    empresa: "empresas",
    processo: "processos",
  };

  const normalizedKey = key.toLowerCase();
  return pluralMap[normalizedKey] || key;
};

/**
 * Parses and normalizes values
 */
export const parseValue = (value: string): string => {
  if (!value || typeof value !== "string") return value;

  const normalizedInput = value.toLowerCase();

  // Check PARSED_VALUES first
  for (const key in PARSED_VALUES) {
    if (key.toLowerCase() === normalizedInput) {
      return PARSED_VALUES[key as keyof typeof PARSED_VALUES];
    }
  }

  // Legacy fallbacks
  const legacyValues: Record<string, string> = {
    "sim": "Sim",
    "nao": "Não",
    "true": "Sim",
    "false": "Não",
    "masculino": "Masculino",
    "feminino": "Feminino",
    "m": "Masculino",
    "f": "Feminino"
  };

  return legacyValues[normalizedInput] || value;
};

export const getImageAsDataUrl = (imagePath: string): string => {
  try {
    if (fs.existsSync(imagePath)) {
      const imageBuffer = fs.readFileSync(imagePath);
      const ext = path.extname(imagePath).toLowerCase();
      const mimeType = ext === '.png' ? 'image/png' : 'image/jpeg';
      return `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
    }
  } catch (error) {
    console.warn(`Failed to load image: ${imagePath}`, error);
  }
  return '';
};
