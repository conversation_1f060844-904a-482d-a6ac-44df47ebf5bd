// Package services services/cache.go
package services

import (
	"bytes"
	"compress/gzip"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
)

var (
	rdb *redis.Client
	ctx = context.Background()
	compressionThreshold = 1024 // Compress PDFs larger than 1KB
	cacheMetadataExpiry = time.Hour * 24 * 7 // Keep metadata for 7 days
	cacheDataExpiry = time.Hour * 24 // Keep PDF data for 24 hours
)

type CacheMetadata struct {
	Size          int       `json:"size"`
	Compressed    bool      `json:"compressed"`
	GeneratedAt   time.Time `json:"generated_at"`
	AccessCount   int       `json:"access_count"`
	LastAccessed  time.Time `json:"last_accessed"`
}

func init() {
	redisAddr := os.Getenv("REDIS_ADDR")
	if redisAddr == "" {
		redisAddr = "localhost:6379"
	}
	log.Info().Str("REDIS_ADDR", redisAddr).Msg("Initializing Redis client")
	rdb = redis.NewClient(&redis.Options{
		Addr: redisAddr,
	})
}

// hashHTML creates a SHA256 hash of the HTML string.
func hashHTML(html string) string {
	hash := sha256.Sum256([]byte(html))
	return hex.EncodeToString(hash[:])
}

// CheckCache checks if a PDF for the given HTML is already cached in Redis.
// Uses tiered caching with metadata and optional compression.
func CheckCache(html string) ([]byte, bool) {
	hash := hashHTML(html)
	metaKey := fmt.Sprintf("%s:meta", hash)
	dataKey := fmt.Sprintf("%s:data", hash)

	// Check metadata first
	metaBytes, err := rdb.Get(ctx, metaKey).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error().Err(err).Str("hash", hash).Msg("Redis metadata check failed")
		} else {
			log.Info().Str("hash", hash).Msg("Redis cache miss")
		}
		return nil, false
	}

	// Parse metadata
	var meta CacheMetadata
	if err := json.Unmarshal(metaBytes, &meta); err != nil {
		log.Error().Err(err).Str("hash", hash).Msg("Failed to parse cache metadata")
		return nil, false
	}

	// Get PDF data
	data, err := rdb.Get(ctx, dataKey).Bytes()
	if err != nil {
		log.Error().Err(err).Str("hash", hash).Msg("Cache metadata exists but data missing")
		// Clean up orphaned metadata
		rdb.Del(ctx, metaKey)
		return nil, false
	}

	// Decompress if needed
	if meta.Compressed {
		gzReader, err := gzip.NewReader(bytes.NewReader(data))
		if err != nil {
			log.Error().Err(err).Str("hash", hash).Msg("Failed to decompress cached PDF")
			return nil, false
		}
		defer gzReader.Close()

		var buf bytes.Buffer
		if _, err := io.Copy(&buf, gzReader); err != nil {
			log.Error().Err(err).Str("hash", hash).Msg("Failed to read decompressed PDF")
			return nil, false
		}
		data = buf.Bytes()
	}

	// Update access statistics
	meta.AccessCount++
	meta.LastAccessed = time.Now()
	updatedMetaBytes, _ := json.Marshal(meta)
	rdb.Set(ctx, metaKey, updatedMetaBytes, cacheMetadataExpiry)

	log.Info().Str("hash", hash).Int("size", len(data)).Bool("was_compressed", meta.Compressed).Int("access_count", meta.AccessCount).Msg("Redis cache hit")
	return data, true
}

// WriteCache writes the PDF data to the Redis cache with compression and metadata.
func WriteCache(html string, data []byte) error {
	hash := hashHTML(html)
	metaKey := fmt.Sprintf("%s:meta", hash)
	dataKey := fmt.Sprintf("%s:data", hash)

	originalSize := len(data)
	compressed := false
	cacheData := data

	// Compress large PDFs
	if originalSize > compressionThreshold {
		var buf bytes.Buffer
		gzWriter := gzip.NewWriter(&buf)
		if _, err := gzWriter.Write(data); err != nil {
			log.Error().Err(err).Str("hash", hash).Msg("Failed to compress PDF for cache")
			// Continue with uncompressed data
		} else {
			if err := gzWriter.Close(); err != nil {
				log.Error().Err(err).Str("hash", hash).Msg("Failed to close compressor")
			} else {
				compressedSize := buf.Len()
				// Only use compression if it actually saves space
				if compressedSize < originalSize {
					cacheData = buf.Bytes()
					compressed = true
					log.Debug().Int("original_size", originalSize).Int("compressed_size", compressedSize).Float64("compression_ratio", float64(compressedSize)/float64(originalSize)).Msg("Compressed PDF for cache")
				}
			}
		}
	}

	// Create metadata
	meta := CacheMetadata{
		Size:         originalSize,
		Compressed:   compressed,
		GeneratedAt:  time.Now(),
		AccessCount:  0,
		LastAccessed: time.Now(),
	}

	metaBytes, err := json.Marshal(meta)
	if err != nil {
		log.Error().Err(err).Str("hash", hash).Msg("Failed to marshal cache metadata")
		return err
	}

	// Use pipeline for atomic write
	pipe := rdb.Pipeline()
	pipe.Set(ctx, metaKey, metaBytes, cacheMetadataExpiry)
	pipe.Set(ctx, dataKey, cacheData, cacheDataExpiry)
	_, err = pipe.Exec(ctx)

	if err != nil {
		log.Error().Err(err).Str("hash", hash).Msg("Failed to write to Redis cache")
		return err
	}

	log.Info().Str("hash", hash).Int("original_size", originalSize).Int("stored_size", len(cacheData)).Bool("compressed", compressed).Msg("Successfully wrote to Redis cache")
	return nil
}
