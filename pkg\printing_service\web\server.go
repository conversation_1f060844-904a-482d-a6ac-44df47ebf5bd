package web

// http/server.go
// Package httpserver provides the HTTP server implementation for the printing service.
import (
	rootroute "snap-printer/pkg/printing_service/routes"
	pdfgenroute "snap-printer/pkg/printing_service/routes/reports/pdf"
	"snap-printer/pkg/printing_service/web/middleware"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

type Server struct {
	router *gin.Engine
}

func New() (*Server, error) {
	log.Info().Msg("Creating new server instance")

	// Create a new Gin router
	r := gin.New()

	// Add middlewares
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.CORSMiddleware())

	// Serve static assets
	r.Static("/templates", "./templates")
	log.Info().Msg("Static assets configured")

	// Setup routes
	log.Info().Msg("Setting up root routes")
	rootroute.SetupRoutes(r)
	log.Info().Msg("Setting up PDF generation routes")
	pdfgenroute.SetupRoutes(r)
	log.Info().Msg("All routes configured")

	return &Server{router: r}, nil
}

// Run starts the HTTP server.
func (s *Server) Run() {
	log.Info().Msg("Starting server on 0.0.0.0:8080")
	if err := s.router.Run("0.0.0.0:8080"); err != nil {
		log.Fatal().Err(err).Msg("Failed to start server")
	}
}
