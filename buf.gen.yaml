version: v2
plugins:
  - remote: buf.build/protocolbuffers/go
    out: ./pkg/printing_service/proto
    opt: paths=source_relative
  - remote: buf.build/connectrpc/go
    out: ./pkg/printing_service/proto
    opt: paths=source_relative
  - remote: buf.build/bufbuild/es:v2.5.2
    out: ./pkg/puppeteer_service/proto
    opt: 
      - target=ts
  - remote: buf.build/connectrpc/query-es:v2.1.0
    out: ./pkg/puppeteer_service/proto
    opt:
      - target=ts
  - remote: buf.build/bufbuild/es:v2.5.2
    out: ./pkg/jsx2html/src/proto
    opt:
      - target=ts
      - import_extension=.js
  - remote: buf.build/connectrpc/query-es:v2.1.0
    out: ./pkg/jsx2html/src/proto
    opt:
      - target=ts
      - import_extension=.js
