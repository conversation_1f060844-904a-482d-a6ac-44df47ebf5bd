{{/*
  RenderPrintDiariosOficiais.tmpl
  Expects struct:
  type DiariosOficiaisSection struct {
    Entries []struct {
      Publication string
      Date        string
      Summary     string
      Source      string
      // Add other fields as needed
    }
  }
*/}}

<div class="diarios-oficiais-section">
  <h2 class="section-header">DIÁRIOS OFICIAIS</h2>
  {{if .Entries}}
    <table class="diarios-oficiais-table">
      <thead>
        <tr>
          <th>Publicação</th>
          <th>Data</th>
          <th>Resumo</th>
          <th>Fonte</th>
        </tr>
      </thead>
      <tbody>
        {{range .Entries}}
        <tr>
          <td>{{.Publication}}</td>
          <td>{{.Date}}</td>
          <td>{{.Summary}}</td>
          <td>{{.Source}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhuma publicação encontrada.</div>
  {{end}}
</div> 