// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: html2pdf/v1/service.proto

package html2pdfv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	http "net/http"
	v1 "snap-printer/pkg/html2pdf/v1"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// PdfGeneratorServiceName is the fully-qualified name of the PdfGeneratorService service.
	PdfGeneratorServiceName = "html2pdf.v1.PdfGeneratorService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// PdfGeneratorServiceGenerateProcedure is the fully-qualified name of the PdfGeneratorService's
	// Generate RPC.
	PdfGeneratorServiceGenerateProcedure = "/html2pdf.v1.PdfGeneratorService/Generate"
)

// PdfGeneratorServiceClient is a client for the html2pdf.v1.PdfGeneratorService service.
type PdfGeneratorServiceClient interface {
	Generate(context.Context, *connect.Request[v1.GenerateRequest]) (*connect.ServerStreamForClient[v1.GenerateResponse], error)
}

// NewPdfGeneratorServiceClient constructs a client for the html2pdf.v1.PdfGeneratorService service.
// By default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped
// responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewPdfGeneratorServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) PdfGeneratorServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	pdfGeneratorServiceMethods := v1.File_html2pdf_v1_service_proto.Services().ByName("PdfGeneratorService").Methods()
	return &pdfGeneratorServiceClient{
		generate: connect.NewClient[v1.GenerateRequest, v1.GenerateResponse](
			httpClient,
			baseURL+PdfGeneratorServiceGenerateProcedure,
			connect.WithSchema(pdfGeneratorServiceMethods.ByName("Generate")),
			connect.WithClientOptions(opts...),
		),
	}
}

// pdfGeneratorServiceClient implements PdfGeneratorServiceClient.
type pdfGeneratorServiceClient struct {
	generate *connect.Client[v1.GenerateRequest, v1.GenerateResponse]
}

// Generate calls html2pdf.v1.PdfGeneratorService.Generate.
func (c *pdfGeneratorServiceClient) Generate(ctx context.Context, req *connect.Request[v1.GenerateRequest]) (*connect.ServerStreamForClient[v1.GenerateResponse], error) {
	return c.generate.CallServerStream(ctx, req)
}

// PdfGeneratorServiceHandler is an implementation of the html2pdf.v1.PdfGeneratorService service.
type PdfGeneratorServiceHandler interface {
	Generate(context.Context, *connect.Request[v1.GenerateRequest], *connect.ServerStream[v1.GenerateResponse]) error
}

// NewPdfGeneratorServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewPdfGeneratorServiceHandler(svc PdfGeneratorServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	pdfGeneratorServiceMethods := v1.File_html2pdf_v1_service_proto.Services().ByName("PdfGeneratorService").Methods()
	pdfGeneratorServiceGenerateHandler := connect.NewServerStreamHandler(
		PdfGeneratorServiceGenerateProcedure,
		svc.Generate,
		connect.WithSchema(pdfGeneratorServiceMethods.ByName("Generate")),
		connect.WithHandlerOptions(opts...),
	)
	return "/html2pdf.v1.PdfGeneratorService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case PdfGeneratorServiceGenerateProcedure:
			pdfGeneratorServiceGenerateHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedPdfGeneratorServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedPdfGeneratorServiceHandler struct{}

func (UnimplementedPdfGeneratorServiceHandler) Generate(context.Context, *connect.Request[v1.GenerateRequest], *connect.ServerStream[v1.GenerateResponse]) error {
	return connect.NewError(connect.CodeUnimplemented, errors.New("html2pdf.v1.PdfGeneratorService.Generate is not implemented"))
}
