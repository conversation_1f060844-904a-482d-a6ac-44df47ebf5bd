{{/*
  RenderPrintPossiveisContas.tmpl
  Expects struct:
  type PossiveisContasSection struct {
    Accounts []struct {
      Site     string
      Username string
      Status   string
      // Add other fields as needed
    }
  }
*/}}

<div class="possiveis-contas-section">
  <h2 class="section-header">POSSÍVEIS CONTAS EM SITES</h2>
  {{if .Accounts}}
    <table class="possiveis-contas-table">
      <thead>
        <tr>
          <th>Site</th>
          <th>Usuário</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {{range .Accounts}}
        <tr>
          <td>{{.Site}}</td>
          <td>{{.Username}}</td>
          <td>{{.Status}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhuma conta encontrada.</div>
  {{end}}
</div> 