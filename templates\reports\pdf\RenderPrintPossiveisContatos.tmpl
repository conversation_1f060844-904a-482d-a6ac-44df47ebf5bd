{{/*
  RenderPrintPossiveisContatos.tmpl
  Expects struct:
  type PossiveisContatosSection struct {
    Contacts []struct {
      Name   string
      Type   string
      Value  string
      Source string
      // Add other fields as needed
    }
  }
*/}}

<div class="possiveis-contatos-section">
  <h2 class="section-header">POSSÍVEIS CONTATOS</h2>
  {{if .Contacts}}
    <table class="possiveis-contatos-table">
      <thead>
        <tr>
          <th>Nome</th>
          <th>Tipo</th>
          <th>Valor</th>
          <th>Fonte</th>
        </tr>
      </thead>
      <tbody>
        {{range .Contacts}}
        <tr>
          <td>{{.Name}}</td>
          <td>{{.Type}}</td>
          <td>{{.Value}}</td>
          <td>{{.Source}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum contato encontrado.</div>
  {{end}}
</div> 