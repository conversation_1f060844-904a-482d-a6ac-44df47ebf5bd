services:
  puppeteer:
    build:
      context: .
      dockerfile: Dockerfile_puppeteer
    ports:
      - "3001:3001"
    restart: always
    environment:
      NODE_OPTIONS: "--max-old-space-size=2048"
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: "1024M"
        reservations:
          cpus: "0.25"
          memory: 256M

  redis:
    image: redis:7.2
    ports:
      - "6379:6379"
    restart: always
    deploy:
      resources:
        limits:
          cpus: "0.2"
          memory: 256M
        reservations:
          cpus: "0.1"
          memory: 128M

  go-api:
    build:
      context: .
      dockerfile: Dockerfile_go-api
    ports:
      - "8080:8080"
    depends_on:
      puppeteer:
        condition: service_started
      redis:
        condition: service_started
    environment:
      - REDIS_ADDR=redis:6379
      - PUPPETEER_URL=http://puppeteer:3001/pdf
      - PUPPETEER_GRPC_ADDR=http://puppeteer:3001
      - USE_HTTP_FALLBACK=true
      - GIN_MODE=release
    deploy:
      resources:
        limits:
          cpus: "0.3"
          memory: 256M
        reservations:
          cpus: "0.15"
          memory: 128M

  jsx2html:
    build:
      context: .
      dockerfile: Dockerfile_jsx2html
    ports:
      - "3002:3002"
    restart: always
    deploy:
      resources:
        limits:
          cpus: "0.3"
          memory: 256M
        reservations:
          cpus: "0.15"
          memory: 128M
