{{/*
  RenderPrintDoacoesEnviadas.tmpl
  Expects struct:
  type DoacoesEnviadasSection struct {
    Donations []struct {
      Recipient   string
      Amount      string
      Date        string
      Description string
      // Add other fields as needed
    }
  }
*/}}

<div class="doacoes-enviadas-section">
  <h2 class="section-header">DOAÇÕES ENVIADAS</h2>
  {{if .Donations}}
    <table class="doacoes-enviadas-table">
      <thead>
        <tr>
          <th><PERSON><PERSON>t<PERSON><PERSON></th>
          <th>Valor</th>
          <th>Data</th>
          <th>Descrição</th>
        </tr>
      </thead>
      <tbody>
        {{range .Donations}}
        <tr>
          <td>{{.Recipient}}</td>
          <td>{{.Amount}}</td>
          <td>{{.Date}}</td>
          <td>{{.Description}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhuma doação enviada encontrada.</div>
  {{end}}
</div> 