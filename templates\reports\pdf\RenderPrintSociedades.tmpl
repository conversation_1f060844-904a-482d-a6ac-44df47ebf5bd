{{/*
  RenderPrintSociedades.tmpl
  Expects struct:
  type SociedadesSection struct {
    Companies []struct {
      Name   string
      CNPJ   string
      Role   string
      Status string
      // Add other fields as needed
    }
  }
*/}}

<div class="sociedades-section">
  <h2 class="section-header">SOCIEDADES</h2>
  {{if .Companies}}
    <table class="sociedades-table">
      <thead>
        <tr>
          <th>Nome</th>
          <th>CNPJ</th>
          <th>Função</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {{range .Companies}}
        <tr>
          <td>{{.Name}}</td>
          <td>{{.CNPJ}}</td>
          <td>{{.Role}}</td>
          <td>{{.Status}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhuma sociedade encontrada.</div>
  {{end}}
</div> 