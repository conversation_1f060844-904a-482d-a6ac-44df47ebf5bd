{{/*
  RenderPrintServicosPublicos.tmpl
  Expects struct:
  type ServicosPublicosSection struct {
    Services []struct {
      Service string
      Date    string
      Status  string
      // Add other fields as needed
    }
  }
*/}}

<div class="servicos-publicos-section">
  <h2 class="section-header">SERVIÇOS PÚBLICOS</h2>
  {{if .Services}}
    <table class="servicos-publicos-table">
      <thead>
        <tr>
          <th>Serviço</th>
          <th>Data</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {{range .Services}}
        <tr>
          <td>{{.Service}}</td>
          <td>{{.Date}}</td>
          <td>{{.Status}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum serviço encontrado.</div>
  {{end}}
</div> 