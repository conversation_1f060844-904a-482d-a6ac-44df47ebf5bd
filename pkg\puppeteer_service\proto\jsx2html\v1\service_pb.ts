// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file jsx2html/v1/service.proto (package jsx2html.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { GenerateRequestSchema, GenerateResponseSchema } from "./model_pb";
import { file_jsx2html_v1_model } from "./model_pb";

/**
 * Describes the file jsx2html/v1/service.proto.
 */
export const file_jsx2html_v1_service: GenFile = /*@__PURE__*/
  fileDesc("Chlqc3gyaHRtbC92MS9zZXJ2aWNlLnByb3RvEgtqc3gyaHRtbC52MTJaCg9Kc3gySHRtbFNlcnZpY2USRwoIR2VuZXJhdGUSHC5qc3gyaHRtbC52MS5HZW5lcmF0ZVJlcXVlc3QaHS5qc3gyaHRtbC52MS5HZW5lcmF0ZVJlc3BvbnNlQi9aLXNuYXAtcHJpbnRlci9nZW4vcHJvdG8vanN4Mmh0bWwvdjE7anN4Mmh0bWx2MWIGcHJvdG8z", [file_jsx2html_v1_model]);

/**
 * @generated from service jsx2html.v1.Jsx2HtmlService
 */
export const Jsx2HtmlService: GenService<{
  /**
   * @generated from rpc jsx2html.v1.Jsx2HtmlService.Generate
   */
  generate: {
    methodKind: "unary";
    input: typeof GenerateRequestSchema;
    output: typeof GenerateResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_jsx2html_v1_service, 0);

