{"name": "pdf-service", "version": "0.1.0", "main": "dist/index.js", "scripts": {"build": "tsc && npm run copy-assets", "copy-assets": "copyfiles -u 1 \"src/assets/**/*\" dist/", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts"}, "dependencies": {"@bufbuild/protobuf": "^2.5.2", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-node": "^2.0.2", "@react-pdf/renderer": "^4.3.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "copyfiles": "^2.4.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3"}}