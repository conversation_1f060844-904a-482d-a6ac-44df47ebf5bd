{
  "compilerOptions": {
    "target": "es2022", /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */
    "lib": [
      "es2022"
    ], /* Specify a set of bundled library declaration files that describe the target runtime environment. */
    "module": "commonjs", /* Specify what module code is generated. */
    "moduleResolution": "node", /* Specify how TypeScript looks up a file from a given module specifier. */
    "rootDir": ".", /* Specify the root folder within your source files. */
    "resolveJsonModule": true, /* Enable importing .json files. */
    "outDir": "dist", /* Specify an output folder for all emitted files. */
    "esModuleInterop": true, /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */
    // "preserveSymlinks": true,                         /* Disable resolving symlinks to their realpath. This correlates to the same flag in node. */
    "forceConsistentCasingInFileNames": true, /* Ensure that casing is correct in imports. */
    /* Type Checking */
    "strict": true, /* Enable all strict type-checking options. */
    "skipLibCheck": true /* <PERSON>p type checking all .d.ts files. */
  }
}