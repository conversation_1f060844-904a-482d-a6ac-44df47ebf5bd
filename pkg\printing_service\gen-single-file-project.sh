#!/bin/bash
# filepath: concat_project_files.sh

# Output file
OUTPUT="all_project_files.txt"
> "$OUTPUT" # Clear output file

# Iterate over all .go files (excluding the output file itself)
find . -type f -name "*.go" ! -name "$(basename "$OUTPUT")" | while read -r file; do
    echo "===== START OF FILE: $file =====" >> "$OUTPUT"
    cat "$file" >> "$OUTPUT"
    echo -e "\n===== END OF FILE: $file =====\n" >> "$OUTPUT"
done

echo "All .go files concatenated"