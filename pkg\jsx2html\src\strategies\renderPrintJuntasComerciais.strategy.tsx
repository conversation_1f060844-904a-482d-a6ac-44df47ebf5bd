import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection, ValueWithSource } from '../types/global';
import { translatePropToLabel, translateSource } from '../helpers';

interface RenderPrintJuntasComerciaisProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      razao_social?: ValueWithSource;
      detalhes?: Record<string, ValueWithSource>;
      [key: string]: any;
    }>
  };
}

export const RenderPrintJuntasComerciais: React.FC<RenderPrintJuntasComerciaisProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((juntaComercial, index) => (
        <View key={`junta-comercial-${index}`} style={styles.juntaContainer}>
          {/* Razão Social */}
          {juntaComercial.razao_social && !juntaComercial.razao_social.is_deleted && (
            <View style={styles.razaoSocialContainer}>
              <View style={styles.razaoSocialLabelContainer}>
                <Text style={styles.razaoSocialLabel}>
                  {(juntaComercial.razao_social.label || "Razão Social").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {juntaComercial.razao_social.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.razaoSocialValue}>{juntaComercial.razao_social.value}</Text>
            </View>
          )}

          {/* Detalhes da Junta Comercial */}
          {juntaComercial.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(juntaComercial.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], detailIndex) => (
                    <View key={`detalhe-${detailIndex}`} style={styles.detailsCell}>
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  razaoSocialLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  juntaContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  razaoSocialContainer: {
    marginBottom: 12,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  razaoSocialLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  razaoSocialValue: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});