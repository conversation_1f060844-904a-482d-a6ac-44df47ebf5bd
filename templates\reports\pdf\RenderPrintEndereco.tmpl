{{/*
  RenderPrintEndereco.tmpl
  Expects struct:
  type EnderecoSection struct {
    Addresses []struct {
      Street      string
      Number      string
      Neighborhood string
      City        string
      State       string
      ZipCode     string
      // Add other fields as needed
    }
  }
*/}}

<div class="enderecos-section">
  <h2 class="section-header">ENDEREÇOS</h2>
  {{if .Addresses}}
    <table class="enderecos-table">
      <thead>
        <tr>
          <th>Rua</th>
          <th>Número</th>
          <th>Bairro</th>
          <th>Cidade</th>
          <th>Estado</th>
          <th>CEP</th>
        </tr>
      </thead>
      <tbody>
        {{range .Addresses}}
        <tr>
          <td>{{.Street}}</td>
          <td>{{.Number}}</td>
          <td>{{.Neighborhood}}</td>
          <td>{{.City}}</td>
          <td>{{.State}}</td>
          <td>{{.ZipCode}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum endereço encontrado.</div>
  {{end}}
</div> 