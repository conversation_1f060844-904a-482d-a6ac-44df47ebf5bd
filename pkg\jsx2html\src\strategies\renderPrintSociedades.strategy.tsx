import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection, ValueWithSource } from '../types/global';
import { translatePropToLabel, getSingular, translateSource } from '../helpers';

interface RenderPrintSociedadesProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      razao_social?: ValueWithSource;
      detalhes?: Record<string, ValueWithSource>;
      enderecos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      telefones?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      emails?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      [key: string]: any;
    }>
  };
}

export const RenderPrintSociedades: React.FC<RenderPrintSociedadesProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer} wrap={false}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((sociedade, sociedadeIndex) => (
        !sociedade.is_deleted && (
          <View key={`sociedade-${sociedadeIndex}`} style={styles.sociedadeContainer}>
            {/* Razão Social */}
            {sociedade.razao_social && !sociedade.razao_social.is_deleted && (
              <View style={styles.razaoSocialContainer}>
                <View style={styles.razaoSocialLabelContainer}>
                  <Text style={styles.razaoSocialLabel}>
                    {(sociedade.razao_social.label || "Razão Social").toUpperCase()}
                  </Text>
                  <Text style={styles.sourceText}>
                    {sociedade.razao_social.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                  </Text>
                </View>
                <Text style={styles.razaoSocialValue}>{sociedade.razao_social.value}</Text>
              </View>
            )}

            {/* Detalhes da Sociedade */}
            {sociedade.detalhes && (
              <View style={styles.detalhesContainer}>
                <View style={styles.detalhesGrid}>
                  {Object.entries(sociedade.detalhes as Record<string, ValueWithSource>)
                    .filter(([_, field]) => !field.is_deleted)
                    .map(([key, field], index) => (
                      <View key={`detalhe-${index}`} style={styles.detailsCell}>
                        <View style={styles.infoContainer} wrap={false}>
                          <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                            <Rect width="8" height="8" fill='#CCCCCC' />
                          </Svg>
                          <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                          <Text style={styles.sourceText}>
                            {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>
                        <Text style={styles.value}>{field.value}</Text>
                      </View>
                    ))}
                </View>
              </View>
            )}

            {/* Endereços */}
            {sociedade.enderecos && sociedade.enderecos.length > 0 && (
              <View style={styles.enderecosContainer}>
                <View style={styles.subtitleContainer}>
                  <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                    <Rect width="4" height="4" fill='#889EA3' />
                  </Svg>
                  <Text style={styles.subtitle}>ENDEREÇOS</Text>
                </View>
                {sociedade.enderecos
                  .filter(endereco => !endereco.is_deleted)
                  .map((endereco, index) => (
                    <View key={`endereco-${index}`} style={styles.addressBlock} >
                      <View style={styles.listContainer} wrap={false}>
                        <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                          <Rect width="4" height="4" fill='#889EA3' />
                        </Svg>
                        <Text style={styles.itemTitle}>ENDEREÇO {index + 1}</Text>
                        <Text style={styles.sourceText}>
                          {endereco.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <View style={styles.grid}>
                        {Object.entries(endereco.value)
                          .filter(([_, field]) => !field.is_deleted)
                          .map(([fieldKey, fieldValue], fieldIndex) => (
                            <View key={`field-${fieldIndex}`} style={styles.cell}>
                              <View style={styles.infoContainer}>
                                <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                  <Rect width="8" height="8" fill='#CCCCCC' />
                                </Svg>
                                <Text style={styles.label}>
                                  {translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                </Text>
                                <Text style={styles.sourceText}>
                                  {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                </Text>
                              </View>
                              <Text style={styles.value}>
                                {String(fieldValue.value || "")}
                              </Text>
                            </View>
                          ))}
                      </View>
                    </View>
                  ))}
              </View>
            )}

            {/* Telefones */}
            {sociedade.telefones && sociedade.telefones.length > 0 && (
              <View style={styles.telefonesContainer}>
                <View style={styles.subtitleContainer} wrap={false}>
                  <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                    <Rect width="4" height="4" fill='#889EA3' />
                  </Svg>
                  <Text style={styles.subtitle}>TELEFONES</Text>
                </View>
                <View style={styles.phoneGrid}>
                  {sociedade.telefones
                    .filter(telefone => !telefone.is_deleted)
                    .map((telefone, index) => (
                      <View key={`telefone-${index}`} style={styles.phoneBlock} >
                        <View style={styles.listContainer} wrap={false}>
                          <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                            <Rect width="4" height="4" fill="#889EA3" />
                          </Svg>
                          <Text style={styles.itemTitle}>
                            {translatePropToLabel(getSingular(telefone.label) || 'TELEFONE').toUpperCase()} {index + 1}
                          </Text>
                          <Text style={styles.sourceText}>
                            {telefone.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>
                        <View style={styles.fieldsGrid}>
                          {Object.entries(telefone.value)
                            .filter(([_, field]) => !field.is_deleted)
                            .map(([fieldKey, fieldValue], fieldIndex) => (
                              <View key={`field-${fieldIndex}`} style={styles.cell}>
                                <View style={styles.infoContainer} wrap={false}>
                                  <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                    <Rect width="8" height="8" fill='#CCCCCC' />
                                  </Svg>
                                  <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                  <Text style={styles.sourceText}>
                                    {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                  </Text>
                                </View>
                                <Text style={styles.value}>
                                  {String(fieldValue.value || "")}
                                </Text>
                              </View>
                            ))}
                        </View>
                      </View>
                    ))}
                </View>
              </View>
            )}

            {/* Emails */}
            {sociedade.emails && sociedade.emails.length > 0 && (
              <View style={styles.emailsContainer}>
                <View style={styles.subtitleContainer} wrap={false}>
                  <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                    <Rect width="4" height="4" fill='#889EA3' />
                  </Svg>
                  <Text style={styles.subtitle}>EMAILS</Text>
                </View>
                <View style={styles.emailGrid}>
                  {sociedade.emails
                    .filter(email => !email.is_deleted)
                    .map((email, index) => (
                      <View key={`email-${index}`} style={styles.emailBlock} >
                        <View style={styles.listContainer} wrap={false}>
                          <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                            <Rect width="4" height="4" fill="#889EA3" />
                          </Svg>
                          <Text style={styles.itemTitle}>
                            {translatePropToLabel(getSingular(email.label) || 'EMAIL').toUpperCase()} {index + 1}
                          </Text>
                          <Text style={styles.sourceText}>
                            {email.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                          </Text>
                        </View>
                        <View style={styles.fieldsGrid}>
                          {Object.entries(email.value)
                            .filter(([_, field]) => !field.is_deleted)
                            .map(([fieldKey, fieldValue], fieldIndex) => (
                              <View key={`field-${fieldIndex}`} style={styles.cell}>
                                <View style={styles.infoContainer} wrap={false}>
                                  <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                    <Rect width="8" height="8" fill='#CCCCCC' />
                                  </Svg>
                                  <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                  <Text style={styles.sourceText}>
                                    {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                  </Text>
                                </View>
                                <Text style={styles.value}>
                                  {String(fieldValue.value || "")}
                                </Text>
                              </View>
                            ))}
                        </View>
                      </View>
                    ))}
                </View>
              </View>
            )}
          </View>
        )
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  razaoSocialLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  listContainer: {
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  sociedadeContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  razaoSocialContainer: {
    marginBottom: 12,
  },
  razaoSocialLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  razaoSocialValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  emailsContainer: {
    marginBottom: 8,
  },
  telefonesContainer: {
    marginBottom: 8,
  },
  enderecosContainer: {
    marginBottom: 8,
  },
  emailBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  itemTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  detalhesGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  grid: {
    paddingTop: 6,
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  cell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  // Telefone styles
  phoneGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  phoneBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
  // Endereco styles
  addressBlock: {
    paddingLeft: 8,
    marginBottom: 12,
    borderBottom: '1pt solid #eee',
    paddingBottom: 8,
  },
  // Email styles
  emailGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});