// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: html2pdf/v1/model.proto

package html2pdfv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Html          string                 `protobuf:"bytes,1,opt,name=html,proto3" json:"html,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateRequest) Reset() {
	*x = GenerateRequest{}
	mi := &file_html2pdf_v1_model_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateRequest) ProtoMessage() {}

func (x *GenerateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_html2pdf_v1_model_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateRequest.ProtoReflect.Descriptor instead.
func (*GenerateRequest) Descriptor() ([]byte, []int) {
	return file_html2pdf_v1_model_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateRequest) GetHtml() string {
	if x != nil {
		return x.Html
	}
	return ""
}

type GenerateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chunk         []byte                 `protobuf:"bytes,1,opt,name=chunk,proto3" json:"chunk,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateResponse) Reset() {
	*x = GenerateResponse{}
	mi := &file_html2pdf_v1_model_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateResponse) ProtoMessage() {}

func (x *GenerateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_html2pdf_v1_model_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateResponse.ProtoReflect.Descriptor instead.
func (*GenerateResponse) Descriptor() ([]byte, []int) {
	return file_html2pdf_v1_model_proto_rawDescGZIP(), []int{1}
}

func (x *GenerateResponse) GetChunk() []byte {
	if x != nil {
		return x.Chunk
	}
	return nil
}

var File_html2pdf_v1_model_proto protoreflect.FileDescriptor

const file_html2pdf_v1_model_proto_rawDesc = "" +
	"\n" +
	"\x17html2pdf/v1/model.proto\x12\vhtml2pdf.v1\"%\n" +
	"\x0fGenerateRequest\x12\x12\n" +
	"\x04html\x18\x01 \x01(\tR\x04html\"(\n" +
	"\x10GenerateResponse\x12\x14\n" +
	"\x05chunk\x18\x01 \x01(\fR\x05chunkB@Z>snap-printer/pkg/printing_service/proto/html2pdf/v1;html2pdfv1b\x06proto3"

var (
	file_html2pdf_v1_model_proto_rawDescOnce sync.Once
	file_html2pdf_v1_model_proto_rawDescData []byte
)

func file_html2pdf_v1_model_proto_rawDescGZIP() []byte {
	file_html2pdf_v1_model_proto_rawDescOnce.Do(func() {
		file_html2pdf_v1_model_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_html2pdf_v1_model_proto_rawDesc), len(file_html2pdf_v1_model_proto_rawDesc)))
	})
	return file_html2pdf_v1_model_proto_rawDescData
}

var file_html2pdf_v1_model_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_html2pdf_v1_model_proto_goTypes = []any{
	(*GenerateRequest)(nil),  // 0: html2pdf.v1.GenerateRequest
	(*GenerateResponse)(nil), // 1: html2pdf.v1.GenerateResponse
}
var file_html2pdf_v1_model_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_html2pdf_v1_model_proto_init() }
func file_html2pdf_v1_model_proto_init() {
	if File_html2pdf_v1_model_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_html2pdf_v1_model_proto_rawDesc), len(file_html2pdf_v1_model_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_html2pdf_v1_model_proto_goTypes,
		DependencyIndexes: file_html2pdf_v1_model_proto_depIdxs,
		MessageInfos:      file_html2pdf_v1_model_proto_msgTypes,
	}.Build()
	File_html2pdf_v1_model_proto = out.File
	file_html2pdf_v1_model_proto_goTypes = nil
	file_html2pdf_v1_model_proto_depIdxs = nil
}
