import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection, ValueWithSource } from '../types/global';
import { translatePropToLabel, getSingular, translateSource, getFieldLabel, getFieldValue } from '../helpers';

interface RenderPrintTelefoneProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes?: Array<{
        value: Record<string, {
          value: string;
          label: string;
          source: string[];
          is_deleted: boolean;
        }>;
        label: string;
        source: string[];
        is_deleted: boolean;
      }>
    }>
  };
}

export const RenderPrintTelefone: React.FC<RenderPrintTelefoneProps> = ({ section }) => {
  const validEntries = section.data?.filter(entry => entry.detalhes?.length) || [];

  if (!validEntries.length) return null;

  const allPhones = validEntries.flatMap(entry =>
    entry.detalhes?.filter((detalhe: ValueWithSource) => !detalhe.is_deleted) || []
  );

  if (!allPhones.length) return null;

  return (
    <View style={styles.container}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>
      <View style={styles.grid}>
        {allPhones.map((detalhe, index) => {
          const validFields = Object.entries(detalhe.value)
            .filter(([fieldKey]) => !detalhe.value[fieldKey].is_deleted);
          const sourceValue = detalhe.source;
          if (!validFields.length) return null;

          return (
            <View key={`phone-${index}`} style={styles.phoneBlock} >
              <View style={styles.subtitleContainer} wrap={false}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill="#889EA3" />
                </Svg>
                <Text style={styles.phoneTitle}>
                  {translatePropToLabel(getSingular(detalhe.label) || 'TELEFONE').toUpperCase()} {index + 1}
                </Text>
                <Text style={styles.sourceText}>
                  {sourceValue?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <View style={styles.fieldsGrid}>
                {validFields.map(([fieldKey, fieldValue], fieldIndex) => (
                  <View key={`field-${fieldIndex}`} style={styles.cell}>
                    <View style={styles.infoContainer}>
                      <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                        <Rect width="8" height="8" fill='#CCCCCC' />
                      </Svg>
                      <Text style={styles.label}>{translatePropToLabel(getFieldLabel(fieldKey, fieldValue)).toUpperCase()}</Text>
                      <Text style={styles.sourceText}>
                        {sourceValue?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                      </Text>
                    </View>
                    <Text style={styles.value}>
                      {String(getFieldValue(fieldValue) || "")}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: { width: 4, height: 4, marginRight: 4, marginTop: 1 },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  subtitleContainer: {
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  grid: {
    paddingLeft: 8,
    paddingTop: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  phoneBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  phoneTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
  cell: {
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});