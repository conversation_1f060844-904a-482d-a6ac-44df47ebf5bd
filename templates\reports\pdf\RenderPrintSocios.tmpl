{{/*
  RenderPrintSocios.tmpl
  Expects struct:
  type SociosSection struct {
    Partners []struct {
      Name         string
      CPF          string
      Role         string
      Participation string
      // Add other fields as needed
    }
  }
*/}}

<div class="socios-section">
  <h2 class="section-header">SÓCIOS</h2>
  {{if .Partners}}
    <table class="socios-table">
      <thead>
        <tr>
          <th>Nome</th>
          <th>CPF</th>
          <th>Função</th>
          <th>Participação</th>
        </tr>
      </thead>
      <tbody>
        {{range .Partners}}
        <tr>
          <td>{{.Name}}</td>
          <td>{{.CPF}}</td>
          <td>{{.Role}}</td>
          <td>{{.Participation}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum sócio encontrado.</div>
  {{end}}
</div> 