{{/*
  printStrategyFactory.tmpl
  Strategy pattern for rendering report sections in Go templates.
  Usage: {{template "RenderSection" .}}
*/}}

{{define "RenderSection"}}
  {{- $type := .Type -}}
  {{- if eq $type "dados_pessoais" }}
    {{template "RenderPrintDadosPessoais" .}}
  {{else if eq $type "mandados_de_prisao" }}
    {{template "RenderPrintMandados" .}}
  {{else if eq $type "enderecos" }}
    {{template "RenderPrintEndereco" .}}
  {{else if eq $type "emails" }}
    {{template "RenderPrintEmail" .}}
  {{else if eq $type "telefones" }}
    {{template "RenderPrintTelefone" .}}
  {{else if eq $type "processos" }}
    {{template "RenderPrintProcessos" .}}
  {{else if eq $type "parentes" }}
    {{template "RenderPrintParentes" .}}
  {{else if eq $type "vinculos_empregaticios" }}
    {{template "RenderPrintVinculosEmpregaticios" .}}
  {{else if eq $type "diarios_oficiais_nome" }}
    {{template "RenderPrintDiariosOficiais" .}}
  {{else if eq $type "possiveis_contatos" }}
    {{template "RenderPrintPossiveisContatos" .}}
  {{else if eq $type "possiveis_contas_em_sites" }}
    {{template "RenderPrintPossiveisContas" .}}
  {{else if eq $type "sociedades" }}
    {{template "RenderPrintSociedades" .}}
  {{else if eq $type "socios" }}
    {{template "RenderPrintSocios" .}}
  {{else if eq $type "recursos_publicos_recebidos" }}
    {{template "RenderPrintRecursosPublicos" .}}
  {{else if eq $type "servico_publico" }}
    {{template "RenderPrintServicosPublicos" .}}
  {{else if eq $type "filiacao_partidaria" }}
    {{template "RenderPrintFiliacaoPartidaria" .}}
  {{else if eq $type "doacoes_enviadas_campanha" }}
    {{template "RenderPrintDoacoesEnviadas" .}}
  {{else if eq $type "doacoes_recebidas_campanha" }}
    {{template "RenderPrintDoacoesRecebidas" .}}
  {{else if eq $type "fornecimentos_enviados_campanha" }}
    {{template "RenderPrintFornecimentosEnviados" .}}
  {{else if eq $type "fornecimentos_recebidos_campanha" }}
    {{template "RenderPrintFornecimentosRecebidos" .}}
  {{else if eq $type "juntas_comerciais" }}
    {{template "RenderPrintJuntasComerciais" .}}
  {{else if eq $type "imagens" }}
    {{template "RenderPrintImagens" .}}
  {{else if eq $type "outras_urls" }}
    {{template "RenderPrintOutrasUrls" .}}
  {{else if eq $type "nomes_usuario" }}
    {{template "RenderPrintNomeUsuarios" .}}
  {{else if eq $type "perfis_redes_sociais" }}
    {{template "RenderPrintRedesSociais" .}}
  {{else if eq $type "possiveis_pessoas_relacionadas" }}
    {{template "RenderPrintPossiveisPessoasRelacionadas" .}}
  {{else if eq $type "vinculos_educacionais" }}
    {{template "RenderPrintVinculosEducacionais" .}}
  {{else if eq $type "empresas_relacionadas" }}
    {{template "RenderPrintSociedades" .}}
  {{else if eq $type "diarios_oficiais_cpf" }}
    {{template "RenderPrintDiariosOficiais" .}}
  {{else if eq $type "diarios_oficiais_cnpj" }}
    {{template "RenderPrintDiariosOficiais" .}}
  {{else}}
    <div>Tipo de seção desconhecido: {{$type}}</div>
  {{end}}
{{end}} 