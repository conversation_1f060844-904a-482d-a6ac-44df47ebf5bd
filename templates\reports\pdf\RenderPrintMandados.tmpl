{{/*
  RenderPrintMandados.tmpl
  Expects struct:
  type MandadosSection struct {
    Warrants []struct {
      Number string
      Court  string
      Date   string
      Status string
      // Add other fields as needed
    }
  }
*/}}

<div class="mandados-section">
  <h2 class="section-header">MANDADOS DE PRISÃO</h2>
  {{if .Warrants}}
    <table class="mandados-table">
      <thead>
        <tr>
          <th>Número</th>
          <th>Vara</th>
          <th>Data</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {{range .Warrants}}
        <tr>
          <td>{{.Number}}</td>
          <td>{{.Court}}</td>
          <td>{{.Date}}</td>
          <td>{{.Status}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum mandado encontrado.</div>
  {{end}}
</div> 