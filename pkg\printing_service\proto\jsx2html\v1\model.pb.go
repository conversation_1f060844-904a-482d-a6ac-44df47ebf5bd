// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: jsx2html/v1/model.proto

package jsx2htmlv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *ReportMetadata        `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Sections      []*ReportSection       `protobuf:"bytes,2,rep,name=sections,proto3" json:"sections,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateRequest) Reset() {
	*x = GenerateRequest{}
	mi := &file_jsx2html_v1_model_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateRequest) ProtoMessage() {}

func (x *GenerateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_jsx2html_v1_model_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateRequest.ProtoReflect.Descriptor instead.
func (*GenerateRequest) Descriptor() ([]byte, []int) {
	return file_jsx2html_v1_model_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateRequest) GetMetadata() *ReportMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GenerateRequest) GetSections() []*ReportSection {
	if x != nil {
		return x.Sections
	}
	return nil
}

type ReportMetadata struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ReportId          string                 `protobuf:"bytes,1,opt,name=report_id,proto3" json:"report_id,omitempty"`
	ReportStatus      string                 `protobuf:"bytes,2,opt,name=report_status,proto3" json:"report_status,omitempty"`
	ReportType        string                 `protobuf:"bytes,3,opt,name=report_type,proto3" json:"report_type,omitempty"`
	ReportSearchArgs  *structpb.Struct       `protobuf:"bytes,4,opt,name=report_search_args,proto3" json:"report_search_args,omitempty"`
	ReportName        string                 `protobuf:"bytes,5,opt,name=report_name,proto3" json:"report_name,omitempty"`
	CreationAt        string                 `protobuf:"bytes,6,opt,name=creation_at,proto3" json:"creation_at,omitempty"`
	ModifiedAt        string                 `protobuf:"bytes,7,opt,name=modified_at,proto3" json:"modified_at,omitempty"`
	SubjectName       string                 `protobuf:"bytes,8,opt,name=subject_name,proto3" json:"subject_name,omitempty"`
	SubjectMotherName string                 `protobuf:"bytes,9,opt,name=subject_mother_name,proto3" json:"subject_mother_name,omitempty"`
	SubjectAge        int32                  `protobuf:"varint,10,opt,name=subject_age,proto3" json:"subject_age,omitempty"`
	SubjectSex        string                 `protobuf:"bytes,11,opt,name=subject_sex,proto3" json:"subject_sex,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ReportMetadata) Reset() {
	*x = ReportMetadata{}
	mi := &file_jsx2html_v1_model_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportMetadata) ProtoMessage() {}

func (x *ReportMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_jsx2html_v1_model_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportMetadata.ProtoReflect.Descriptor instead.
func (*ReportMetadata) Descriptor() ([]byte, []int) {
	return file_jsx2html_v1_model_proto_rawDescGZIP(), []int{1}
}

func (x *ReportMetadata) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *ReportMetadata) GetReportStatus() string {
	if x != nil {
		return x.ReportStatus
	}
	return ""
}

func (x *ReportMetadata) GetReportType() string {
	if x != nil {
		return x.ReportType
	}
	return ""
}

func (x *ReportMetadata) GetReportSearchArgs() *structpb.Struct {
	if x != nil {
		return x.ReportSearchArgs
	}
	return nil
}

func (x *ReportMetadata) GetReportName() string {
	if x != nil {
		return x.ReportName
	}
	return ""
}

func (x *ReportMetadata) GetCreationAt() string {
	if x != nil {
		return x.CreationAt
	}
	return ""
}

func (x *ReportMetadata) GetModifiedAt() string {
	if x != nil {
		return x.ModifiedAt
	}
	return ""
}

func (x *ReportMetadata) GetSubjectName() string {
	if x != nil {
		return x.SubjectName
	}
	return ""
}

func (x *ReportMetadata) GetSubjectMotherName() string {
	if x != nil {
		return x.SubjectMotherName
	}
	return ""
}

func (x *ReportMetadata) GetSubjectAge() int32 {
	if x != nil {
		return x.SubjectAge
	}
	return 0
}

func (x *ReportMetadata) GetSubjectSex() string {
	if x != nil {
		return x.SubjectSex
	}
	return ""
}

type ReportSection struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle      string                 `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	Subsection    string                 `protobuf:"bytes,3,opt,name=subsection,proto3" json:"subsection,omitempty"`
	Source        []string               `protobuf:"bytes,4,rep,name=source,proto3" json:"source,omitempty"`
	DataCount     int32                  `protobuf:"varint,5,opt,name=data_count,proto3" json:"data_count,omitempty"`
	IsDeleted     bool                   `protobuf:"varint,6,opt,name=is_deleted,proto3" json:"is_deleted,omitempty"`
	Data          []*structpb.Struct     `protobuf:"bytes,7,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportSection) Reset() {
	*x = ReportSection{}
	mi := &file_jsx2html_v1_model_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSection) ProtoMessage() {}

func (x *ReportSection) ProtoReflect() protoreflect.Message {
	mi := &file_jsx2html_v1_model_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSection.ProtoReflect.Descriptor instead.
func (*ReportSection) Descriptor() ([]byte, []int) {
	return file_jsx2html_v1_model_proto_rawDescGZIP(), []int{2}
}

func (x *ReportSection) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ReportSection) GetSubtitle() string {
	if x != nil {
		return x.Subtitle
	}
	return ""
}

func (x *ReportSection) GetSubsection() string {
	if x != nil {
		return x.Subsection
	}
	return ""
}

func (x *ReportSection) GetSource() []string {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *ReportSection) GetDataCount() int32 {
	if x != nil {
		return x.DataCount
	}
	return 0
}

func (x *ReportSection) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ReportSection) GetData() []*structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

type GenerateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Html          string                 `protobuf:"bytes,1,opt,name=html,proto3" json:"html,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateResponse) Reset() {
	*x = GenerateResponse{}
	mi := &file_jsx2html_v1_model_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateResponse) ProtoMessage() {}

func (x *GenerateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_jsx2html_v1_model_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateResponse.ProtoReflect.Descriptor instead.
func (*GenerateResponse) Descriptor() ([]byte, []int) {
	return file_jsx2html_v1_model_proto_rawDescGZIP(), []int{3}
}

func (x *GenerateResponse) GetHtml() string {
	if x != nil {
		return x.Html
	}
	return ""
}

var File_jsx2html_v1_model_proto protoreflect.FileDescriptor

const file_jsx2html_v1_model_proto_rawDesc = "" +
	"\n" +
	"\x17jsx2html/v1/model.proto\x12\vjsx2html.v1\x1a\x1cgoogle/protobuf/struct.proto\"\x82\x01\n" +
	"\x0fGenerateRequest\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.jsx2html.v1.ReportMetadataR\bmetadata\x126\n" +
	"\bsections\x18\x02 \x03(\v2\x1a.jsx2html.v1.ReportSectionR\bsections\"\xbf\x03\n" +
	"\x0eReportMetadata\x12\x1c\n" +
	"\treport_id\x18\x01 \x01(\tR\treport_id\x12$\n" +
	"\rreport_status\x18\x02 \x01(\tR\rreport_status\x12 \n" +
	"\vreport_type\x18\x03 \x01(\tR\vreport_type\x12G\n" +
	"\x12report_search_args\x18\x04 \x01(\v2\x17.google.protobuf.StructR\x12report_search_args\x12 \n" +
	"\vreport_name\x18\x05 \x01(\tR\vreport_name\x12 \n" +
	"\vcreation_at\x18\x06 \x01(\tR\vcreation_at\x12 \n" +
	"\vmodified_at\x18\a \x01(\tR\vmodified_at\x12\"\n" +
	"\fsubject_name\x18\b \x01(\tR\fsubject_name\x120\n" +
	"\x13subject_mother_name\x18\t \x01(\tR\x13subject_mother_name\x12 \n" +
	"\vsubject_age\x18\n" +
	" \x01(\x05R\vsubject_age\x12 \n" +
	"\vsubject_sex\x18\v \x01(\tR\vsubject_sex\"\xe6\x01\n" +
	"\rReportSection\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x1a\n" +
	"\bsubtitle\x18\x02 \x01(\tR\bsubtitle\x12\x1e\n" +
	"\n" +
	"subsection\x18\x03 \x01(\tR\n" +
	"subsection\x12\x16\n" +
	"\x06source\x18\x04 \x03(\tR\x06source\x12\x1e\n" +
	"\n" +
	"data_count\x18\x05 \x01(\x05R\n" +
	"data_count\x12\x1e\n" +
	"\n" +
	"is_deleted\x18\x06 \x01(\bR\n" +
	"is_deleted\x12+\n" +
	"\x04data\x18\a \x03(\v2\x17.google.protobuf.StructR\x04data\"&\n" +
	"\x10GenerateResponse\x12\x12\n" +
	"\x04html\x18\x01 \x01(\tR\x04htmlB@Z>snap-printer/pkg/printing_service/proto/jsx2html/v1;jsx2htmlv1b\x06proto3"

var (
	file_jsx2html_v1_model_proto_rawDescOnce sync.Once
	file_jsx2html_v1_model_proto_rawDescData []byte
)

func file_jsx2html_v1_model_proto_rawDescGZIP() []byte {
	file_jsx2html_v1_model_proto_rawDescOnce.Do(func() {
		file_jsx2html_v1_model_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_jsx2html_v1_model_proto_rawDesc), len(file_jsx2html_v1_model_proto_rawDesc)))
	})
	return file_jsx2html_v1_model_proto_rawDescData
}

var file_jsx2html_v1_model_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_jsx2html_v1_model_proto_goTypes = []any{
	(*GenerateRequest)(nil),  // 0: jsx2html.v1.GenerateRequest
	(*ReportMetadata)(nil),   // 1: jsx2html.v1.ReportMetadata
	(*ReportSection)(nil),    // 2: jsx2html.v1.ReportSection
	(*GenerateResponse)(nil), // 3: jsx2html.v1.GenerateResponse
	(*structpb.Struct)(nil),  // 4: google.protobuf.Struct
}
var file_jsx2html_v1_model_proto_depIdxs = []int32{
	1, // 0: jsx2html.v1.GenerateRequest.metadata:type_name -> jsx2html.v1.ReportMetadata
	2, // 1: jsx2html.v1.GenerateRequest.sections:type_name -> jsx2html.v1.ReportSection
	4, // 2: jsx2html.v1.ReportMetadata.report_search_args:type_name -> google.protobuf.Struct
	4, // 3: jsx2html.v1.ReportSection.data:type_name -> google.protobuf.Struct
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_jsx2html_v1_model_proto_init() }
func file_jsx2html_v1_model_proto_init() {
	if File_jsx2html_v1_model_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_jsx2html_v1_model_proto_rawDesc), len(file_jsx2html_v1_model_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_jsx2html_v1_model_proto_goTypes,
		DependencyIndexes: file_jsx2html_v1_model_proto_depIdxs,
		MessageInfos:      file_jsx2html_v1_model_proto_msgTypes,
	}.Build()
	File_jsx2html_v1_model_proto = out.File
	file_jsx2html_v1_model_proto_goTypes = nil
	file_jsx2html_v1_model_proto_depIdxs = nil
}
