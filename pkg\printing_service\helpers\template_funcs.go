package helpers

import (
	"html/template"
	domain "snap-printer/pkg/printing_service/domain/reports/pdf"
	"strings"
)

// GetTemplateFuncMap returns a map of custom template functions
func GetTemplateFuncMap() template.FuncMap {
	return template.FuncMap{
		"toUpper":     strings.ToUpper,
		"getInitials": getInitials,
		"add":         add,
		"div":         div,
	}
}

// getInitials extracts initials from a full name
func getInitials(name string) string {
	if name == "" {
		return ""
	}

	words := strings.Fields(strings.TrimSpace(name))
	if len(words) == 0 {
		return ""
	}

	var initials strings.Builder
	for _, word := range words {
		if len(word) > 0 {
			initials.WriteString(strings.ToUpper(string(word[0])))
		}
	}

	return initials.String()
}

// add performs addition of two integers
func add(a, b int) int {
	return a + b
}

// div performs integer division
func div(a, b int) int {
	if b == 0 {
		return 0
	}
	return a / b
}

// PaginateSections splits sections into pages with a specified number of sections per page
func PaginateSections(sections []domain.Section, sectionsPerPage int) [][]domain.Section {
	if sectionsPerPage <= 0 {
		return nil
	}

	length := len(sections)
	if length == 0 {
		return nil
	}

	var pages [][]domain.Section

	for i := 0; i < length; i += sectionsPerPage {
		end := i + sectionsPerPage
		if end > length {
			end = length
		}
		pages = append(pages, sections[i:end])
	}

	return pages
}
