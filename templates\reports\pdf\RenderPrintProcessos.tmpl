{{/*
  RenderPrintProcessos.tmpl
  Expects struct:
  type ProcessosSection struct {
    Cases []struct {
      Number      string
      Court       string
      Date        string
      Status      string
      Description string
      // Add other fields as needed
    }
  }
*/}}

<div class="processos-section">
  <h2 class="section-header">PROCESSOS</h2>
  {{if .Cases}}
    <table class="processos-table">
      <thead>
        <tr>
          <th>Número</th>
          <th>Vara</th>
          <th>Data</th>
          <th>Status</th>
          <th>Descrição</th>
        </tr>
      </thead>
      <tbody>
        {{range .Cases}}
        <tr>
          <td>{{.Number}}</td>
          <td>{{.Court}}</td>
          <td>{{.Date}}</td>
          <td>{{.Status}}</td>
          <td>{{.Description}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum processo encontrado.</div>
  {{end}}
</div> 