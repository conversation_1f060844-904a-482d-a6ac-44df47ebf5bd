#!/bin/bash

# Test script for the new PDF generation endpoint
# This script tests the /pdf/generate endpoint that mirrors the TypeScript service

echo "Testing PDF Generation Endpoint..."
echo "=================================="

# Set the server URL (adjust if needed)
SERVER_URL="http://localhost:8080"

# Check if server is running
echo "Checking if server is running..."
if ! curl -s "$SERVER_URL/" > /dev/null; then
    echo "Error: Server is not running at $SERVER_URL"
    echo "Please start the server first with: go run ."
    exit 1
fi

echo "Server is running!"
echo ""

# Test the new PDF generation endpoint
echo "Testing /pdf/generate endpoint..."
echo "Sending request with test payload..."

# Choose which payload to use
USE_ORIGINAL_FORMAT=false  # Set to true to use test-report-payload.json, false to use reports-pdf-data.json

if [ "$USE_ORIGINAL_FORMAT" = true ]; then
  # Use standard test payload
  PAYLOAD_PATH="test-report-payload.json"
  CONTENT_TYPE="application/json"
  echo "Using standard JSON format with $PAYLOAD_PATH"
else
  # Use reports-pdf-data payload (fixed version)
  PAYLOAD_PATH="reports-pfd-data-fixed.json"
  CONTENT_TYPE="application/json"
  echo "Using reports-pdf-data format with $PAYLOAD_PATH"
fi

# Check if payload file exists
if [ ! -f "$PAYLOAD_PATH" ]; then
  echo "Error: Test payload file not found: $PAYLOAD_PATH"
  exit 1
fi

# Start timing for parsing input
PARSE_START=$(date +%s.%N)
PAYLOAD=$(cat "$PAYLOAD_PATH")
PARSE_END=$(date +%s.%N)
PARSE_TIME=$(echo "$PARSE_END - $PARSE_START" | bc)
echo "Parse input time: ${PARSE_TIME}s"

# Start timing for overall process
TOTAL_START=$(date +%s.%N)

# Make the request and save the response (this includes PDF creation time)
PDF_CREATE_START=$(date +%s.%N)
RESPONSE=$(curl -X POST \
  -H "Content-Type: $CONTENT_TYPE" \
  -H "Authorization: Bearer your-access-key-here" \
  -d @"$PAYLOAD_PATH" \
  "$SERVER_URL/pdf/generate" \
  --output test-output.pdf \
  --write-out "HTTP Status: %{http_code}\nSize: %{size_download} bytes\n")
PDF_CREATE_END=$(date +%s.%N)
PDF_CREATE_TIME=$(echo "$PDF_CREATE_END - $PDF_CREATE_START" | bc)

# Stop timing for overall process
TOTAL_END=$(date +%s.%N)
TOTAL_TIME=$(echo "$TOTAL_END - $TOTAL_START" | bc)

echo "$RESPONSE"
echo "Parse input time: ${PARSE_TIME}s"
echo "Create PDF time: ${PDF_CREATE_TIME}s"
echo "Total time: ${TOTAL_TIME}s"

# Check if PDF was created
if [ -f "test-output.pdf" ]; then
    echo ""
    echo "Success! PDF generated and saved as test-output.pdf"
    echo "File size: $(ls -lh test-output.pdf | awk '{print $5}')"
else
    echo ""
    echo "Error: PDF file was not created"
fi

echo ""
echo "Test completed!"
