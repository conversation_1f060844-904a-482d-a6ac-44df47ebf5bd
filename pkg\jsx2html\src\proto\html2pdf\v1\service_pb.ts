// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file html2pdf/v1/service.proto (package html2pdf.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { GenerateRequestSchema, GenerateResponseSchema } from "./model_pb";
import { file_html2pdf_v1_model } from "./model_pb";

/**
 * Describes the file html2pdf/v1/service.proto.
 */
export const file_html2pdf_v1_service: GenFile = /*@__PURE__*/
  fileDesc("ChlodG1sMnBkZi92MS9zZXJ2aWNlLnByb3RvEgtodG1sMnBkZi52MTJgChNQZGZHZW5lcmF0b3JTZXJ2aWNlEkkKCEdlbmVyYXRlEhwuaHRtbDJwZGYudjEuR2VuZXJhdGVSZXF1ZXN0Gh0uaHRtbDJwZGYudjEuR2VuZXJhdGVSZXNwb25zZTABQkBaPnNuYXAtcHJpbnRlci9wa2cvcHJpbnRpbmdfc2VydmljZS9wcm90by9odG1sMnBkZi92MTtodG1sMnBkZnYxYgZwcm90bzM", [file_html2pdf_v1_model]);

/**
 * @generated from service html2pdf.v1.PdfGeneratorService
 */
export const PdfGeneratorService: GenService<{
  /**
   * @generated from rpc html2pdf.v1.PdfGeneratorService.Generate
   */
  generate: {
    methodKind: "server_streaming";
    input: typeof GenerateRequestSchema;
    output: typeof GenerateResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_html2pdf_v1_service, 0);

