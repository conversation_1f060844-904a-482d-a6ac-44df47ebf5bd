{{/*
  RenderPrintTelefone.tmpl
  Expects struct:
  type TelefoneSection struct {
    Phones []struct {
      Number   string
      Type     string
      Verified bool
      // Add other fields as needed
    }
  }
*/}}

<div class="telefones-section">
  <h2 class="section-header">TELEFONES</h2>
  {{if .Phones}}
    <table class="telefones-table">
      <thead>
        <tr>
          <th>Número</th>
          <th>Tipo</th>
          <th>Verificado</th>
        </tr>
      </thead>
      <tbody>
        {{range .Phones}}
        <tr>
          <td>{{.Number}}</td>
          <td>{{.Type}}</td>
          <td>{{if .Verified}}Sim{{else}}Não{{end}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum telefone encontrado.</div>
  {{end}}
</div> 