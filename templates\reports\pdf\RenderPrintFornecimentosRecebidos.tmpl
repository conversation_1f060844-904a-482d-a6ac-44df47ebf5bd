{{/*
  RenderPrintFornecimentosRecebidos.tmpl
  Expects struct:
  type FornecimentosRecebidosSection struct {
    Supplies []struct {
      Supplier    string
      Amount      string
      Date        string
      Description string
      // Add other fields as needed
    }
  }
*/}}

<div class="fornecimentos-recebidos-section">
  <h2 class="section-header">FORNECIMENTOS RECEBIDOS</h2>
  {{if .Supplies}}
    <table class="fornecimentos-recebidos-table">
      <thead>
        <tr>
          <th>Fornecedor</th>
          <th>Valor</th>
          <th>Data</th>
          <th>Descrição</th>
        </tr>
      </thead>
      <tbody>
        {{range .Supplies}}
        <tr>
          <td>{{.Supplier}}</td>
          <td>{{.Amount}}</td>
          <td>{{.Date}}</td>
          <td>{{.Description}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum fornecimento recebido encontrado.</div>
  {{end}}
</div> 