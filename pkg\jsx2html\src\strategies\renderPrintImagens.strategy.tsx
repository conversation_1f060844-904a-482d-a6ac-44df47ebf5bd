import React from 'react';
import { View, Text, StyleSheet, Svg, Rect, Image, Link } from '@react-pdf/renderer';
import { ReportSection, ValueWithSource } from '../types/global';
import { translatePropToLabel, getSingular, translateSource } from '../helpers';
import { getFieldValue, isValidUrl, isBase64Image, formatImageSrc } from '../helpers';

interface RenderPrintImagensProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
    }>
  };
}

export const RenderPrintImagens: React.FC<RenderPrintImagensProps> = ({ section }) => {
  const validEntries = section.data?.filter(entry => entry.detalhes?.length) || [];

  if (!validEntries.length) return null;

  const allImages = validEntries.flatMap(entry =>
    entry.detalhes?.filter((detalhe: ValueWithSource) => !detalhe.is_deleted) || []
  );

  if (!allImages.length) return null;

  const noRenderProps = ['label default key', 'bookmark'];

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>
      <View style={styles.grid}>
        {allImages.map((detalhe, index) => {
          const validFields = Object.entries(detalhe.value)
            .filter(([fieldKey]) => !noRenderProps.includes(fieldKey) && !detalhe.value[fieldKey].is_deleted);
          const sourceValue = detalhe.source;
          if (!validFields.length) return null;

          return (
            <View key={`image-${index}`} style={styles.imageBlock} >
              <View style={styles.subtitleContainer} wrap={false}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill="#889EA3" />
                </Svg>
                <Text style={styles.imageTitle}>
                  {translatePropToLabel(getSingular(detalhe.label) || 'IMAGEM').toUpperCase()} {index + 1}
                </Text>
                <Text style={styles.sourceText}>
                  {sourceValue?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <View style={styles.fieldsGrid}>
                {validFields.map(([fieldKey, fieldValue], fieldIndex) => {
                  const imageUrl = String(getFieldValue(fieldValue) || "");
                  const isHttpUrl = isValidUrl(imageUrl) && !isBase64Image(imageUrl);

                  return (
                    <View key={`field-${fieldIndex}`} style={styles.cell}>

                      {(isValidUrl(imageUrl) || isBase64Image(imageUrl)) && (
                        <View style={styles.imageContainer} wrap={false}>
                          <Image src={formatImageSrc(imageUrl)} style={styles.image} />
                        </View>
                      )}

                      {isHttpUrl && (
                        <Link src={imageUrl} style={styles.urlText}>
                          <Text>
                            {imageUrl?.length > 60 ? `${imageUrl?.substring(0, 40)}...` : imageUrl}
                          </Text>
                        </Link>
                      )}
                    </View>
                  );
                })}
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: { width: 4, height: 4, marginRight: 4, marginTop: 1 },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  subtitleContainer: {
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  grid: {
    paddingLeft: 8,
    paddingTop: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  imageBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  imageTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
  cell: {
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  urlText: {
    paddingLeft: 8,
    fontSize: 10,
    maxWidth: '100%',
    width: '100%',
    color: '#000000',
  },
  imageContainer: {
    paddingLeft: 8,
    marginBottom: 4,
    alignItems: 'flex-start',
  },
  image: {
    width: 'auto',
    height: 'auto',
    maxWidth: '100%',
    maxHeight: 150,
    alignSelf: 'flex-start',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});