---
description: Task to convert .tsx files in go .tmpl
globs: *.tsx *.tmpl *.css *.ts
alwaysApply: false
---
# TypeScript to Go Template Migration Assistant

I need help migrating a TypeScript React project to Go templates while maintaining the existing architecture and business logic.

## Current Architecture (TypeScript/React)
- **File Format**: `.tsx` files using React components
- **Design Pattern**: Strategy pattern for layout rendering
- **Data Handling**: Iterates over data objects with conditional layout application based on data properties
- **Styling**: Inline styles using <PERSON>act's `style` objects notation
- **Structure**: Organized with reusable components and conditional rendering logic

## Target Architecture (Go)
- **File Format**: `.tmpl` template files
- **Design Pattern**: Maintain strategy pattern organization using Go template partials
- **Data Handling**: Preserve the same data iteration and conditional layout logic
- **Styling**: Convert inline styles to CSS classes and external stylesheets
- **Structure**: Organize templates as partials orchestrated by strategy pattern

## Specific Requirements

### 1. Template Structure
- Convert React components to Go template partials
- Maintain the strategy pattern for layout selection
- Preserve conditional rendering logic using Go template syntax
- Keep the same data iteration patterns

### 2. Styling Migration
- Convert React `style` objects to CSS classes
- Generate external CSS files with appropriate class names
- Maintain visual consistency with original styling
- Use semantic CSS class naming conventions

### 3. Type Safety
- Leverage existing TypeScript types to create corresponding Go structs
- Ensure type safety is maintained in the Go implementation
- Create proper struct definitions for data models

### 4. Asset Management
- Resolve font loading issues
- Fix image loading problems (currently not working correctly)
- Properly configure CSS asset serving
- Implement correct asset path handling in Go

### 5. Business Logic Preservation
- **CRITICAL**: Do not modify any existing business rules
- **CRITICAL**: Do not change any existing application configurations
- Maintain the same data flow and processing logic
- Preserve all conditional logic and data transformations

## Expected Deliverables
1. Go template files (.tmpl) with strategy pattern organization
2. CSS files with converted styles
3. Go struct definitions based on TypeScript types
4. Asset serving configuration
5. Documentation explaining the migration approach

## Constraints
- Maintain existing functionality without modifications
- Preserve all business logic and rules
- Keep the same user experience and visual appearance
- Ensure proper asset loading (fonts, images, CSS)

Please analyze the provided TypeScript files and create the equivalent Go template implementation following these requirements.