import { ServiceImpl } from "@connectrpc/connect";
import { GenerateRequest, GenerateResponse } from "../proto/jsx2html/v1/model_pb";
import { ReportDocument } from '../components/ReportDocument';
import { renderToStaticMarkup } from 'react-dom/server';

// Connect streaming handler for Jsx2HtmlService.Generate
export const Jsx2Pdf: ServiceImpl<any>["generate"] = async function* (req: GenerateRequest) {
  try {
    // Validate the request
    if (!req.sections || !Array.isArray(req.sections) || req.sections.length === 0) {
      throw new Error("Invalid sections provided in the request.");
    }
    if (!req.metadata) {
      throw new Error("Metadata is required in the request.");
    }

    // Render the HTML from the request (sections, metadata)
    const html = renderToStaticMarkup(
      await ReportDocument({
        sections: req.sections,
        metadata: req.metadata
      })
    ) ?? '';

    // Yield the HTML as a single chunk (could be split if needed)
    yield { chunk: new TextEncoder().encode(html) } as GenerateResponse;
  } catch (error) {
    // On error, yield an empty chunk (or handle as per Connect error handling)
    yield { chunk: new Uint8Array() } as GenerateResponse;
  }
};
