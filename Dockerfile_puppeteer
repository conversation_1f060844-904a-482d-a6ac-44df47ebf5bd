FROM node:24

WORKDIR /app

COPY ./pkg/puppeteer_service/package.json ./pkg/puppeteer_service/package-lock.json* ./
RUN npm install

COPY ./pkg/puppeteer_service .

RUN npm run build --profile

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nodejs

# Install latest chrome dev package and fonts to support major charsets (Chinese, Japanese, Arabic, Hebrew, Thai and a few others)
RUN apt-get update && apt-get install -y wget && \
  wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
  sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' && \
  apt-get update && \
  apt-get install -y google-chrome-stable fonts-noto-cjk fonts-noto-color-emoji fonts-thai-tlwg fonts-freefont-ttf && \
  rm -rf /var/lib/apt/lists/*

ENV PUPPETEER_SKIP_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
ENV HOME=/home/<USER>

RUN mkdir -p /home/<USER>/home/<USER>

USER nodejs

EXPOSE 3001

CMD ["node", "./dist/index.js"]

