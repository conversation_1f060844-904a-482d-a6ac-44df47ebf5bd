FROM golang:1.24.4-alpine AS builder

WORKDIR /app

COPY go.* ./
RUN go mod download && go mod verify

COPY . ./
RUN CGO_ENABLED=0 GOOS=linux go build -mod=readonly -v -o server ./cmd/printing_service

FROM alpine:3
RUN apk add --no-cache ca-certificates

WORKDIR /app

COPY --from=builder /app/server ./server
COPY ./templates/ ./templates/

# Environment variables for React PDF service
ENV JSX2HTML_SERVICE_URL=http://jsx2html:3002

CMD ["./server"]
