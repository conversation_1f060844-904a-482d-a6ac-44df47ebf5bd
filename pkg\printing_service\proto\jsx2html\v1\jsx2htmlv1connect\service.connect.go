// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: jsx2html/v1/service.proto

package jsx2htmlv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	http "net/http"
	v1 "snap-printer/pkg/printing_service/proto/jsx2html/v1"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// Jsx2HtmlServiceName is the fully-qualified name of the Jsx2HtmlService service.
	Jsx2HtmlServiceName = "jsx2html.v1.Jsx2HtmlService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// Jsx2HtmlServiceGenerateProcedure is the fully-qualified name of the Jsx2HtmlService's Generate
	// RPC.
	Jsx2HtmlServiceGenerateProcedure = "/jsx2html.v1.Jsx2HtmlService/Generate"
)

// Jsx2HtmlServiceClient is a client for the jsx2html.v1.Jsx2HtmlService service.
type Jsx2HtmlServiceClient interface {
	Generate(context.Context, *connect.Request[v1.GenerateRequest]) (*connect.Response[v1.GenerateResponse], error)
}

// NewJsx2HtmlServiceClient constructs a client for the jsx2html.v1.Jsx2HtmlService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewJsx2HtmlServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) Jsx2HtmlServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	jsx2HtmlServiceMethods := v1.File_jsx2html_v1_service_proto.Services().ByName("Jsx2HtmlService").Methods()
	return &jsx2HtmlServiceClient{
		generate: connect.NewClient[v1.GenerateRequest, v1.GenerateResponse](
			httpClient,
			baseURL+Jsx2HtmlServiceGenerateProcedure,
			connect.WithSchema(jsx2HtmlServiceMethods.ByName("Generate")),
			connect.WithClientOptions(opts...),
		),
	}
}

// jsx2HtmlServiceClient implements Jsx2HtmlServiceClient.
type jsx2HtmlServiceClient struct {
	generate *connect.Client[v1.GenerateRequest, v1.GenerateResponse]
}

// Generate calls jsx2html.v1.Jsx2HtmlService.Generate.
func (c *jsx2HtmlServiceClient) Generate(ctx context.Context, req *connect.Request[v1.GenerateRequest]) (*connect.Response[v1.GenerateResponse], error) {
	return c.generate.CallUnary(ctx, req)
}

// Jsx2HtmlServiceHandler is an implementation of the jsx2html.v1.Jsx2HtmlService service.
type Jsx2HtmlServiceHandler interface {
	Generate(context.Context, *connect.Request[v1.GenerateRequest]) (*connect.Response[v1.GenerateResponse], error)
}

// NewJsx2HtmlServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewJsx2HtmlServiceHandler(svc Jsx2HtmlServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	jsx2HtmlServiceMethods := v1.File_jsx2html_v1_service_proto.Services().ByName("Jsx2HtmlService").Methods()
	jsx2HtmlServiceGenerateHandler := connect.NewUnaryHandler(
		Jsx2HtmlServiceGenerateProcedure,
		svc.Generate,
		connect.WithSchema(jsx2HtmlServiceMethods.ByName("Generate")),
		connect.WithHandlerOptions(opts...),
	)
	return "/jsx2html.v1.Jsx2HtmlService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case Jsx2HtmlServiceGenerateProcedure:
			jsx2HtmlServiceGenerateHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedJsx2HtmlServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedJsx2HtmlServiceHandler struct{}

func (UnimplementedJsx2HtmlServiceHandler) Generate(context.Context, *connect.Request[v1.GenerateRequest]) (*connect.Response[v1.GenerateResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("jsx2html.v1.Jsx2HtmlService.Generate is not implemented"))
}
