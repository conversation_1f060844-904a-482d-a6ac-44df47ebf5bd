{{/*
  RenderPrintDadosPessoais.tmpl
  Expects struct:
  type DadosPessoaisSection struct {
    Name         string
    MotherName   string
    Age          int
    Sex          string
    // Add other fields as needed
  }
*/}}

<div class="dados-pessoais-section">
  <h2 class="section-header">DADOS PESSOAIS</h2>
  <div class="dados-row">
    <span class="label">Nome:</span>
    <span class="value">{{.Name}}</span>
  </div>
  <div class="dados-row">
    <span class="label">Nome da Mãe:</span>
    <span class="value">{{if .MotherName}}{{.MotherName}}{{else}}N/A{{end}}</span>
  </div>
  <div class="dados-row">
    <span class="label">Idade:</span>
    <span class="value">{{if .Age}}{{.Age}}{{else}}N/A{{end}}</span>
  </div>
  <div class="dados-row">
    <span class="label">Sexo:</span>
    <span class="value">{{if .Sex}}{{.Sex}}{{else}}N/A{{end}}</span>
  </div>
  <!-- Add more fields as needed -->
</div> 