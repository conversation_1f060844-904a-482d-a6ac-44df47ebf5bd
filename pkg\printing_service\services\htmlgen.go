// Package services services/htmlgen.go
package services

import (
	"bytes"
	"html/template"
	"os"
	"runtime"
	"strings"
	"sync"

	domain "snap-printer/pkg/printing_service/domain/reports/pdf"
	"snap-printer/pkg/printing_service/helpers"

	"github.com/rs/zerolog/log"
)

var (
	// bufPool is a pool of buffers to reduce memory allocations.
	bufPool = sync.Pool{
		New: func() any {
			return new(bytes.Buffer)
		},
	}
	// maxWorkers is the number of concurrent workers for rendering pages.
	maxWorkers = runtime.NumCPU()
)

// RenderPagesParallel renders pages of sections in parallel.
func RenderPagesParallel(tmpl *template.Template, sections []domain.Section) (template.HTML, error) {
	paginatedSections := helpers.PaginateSections(sections, 3)
	if paginatedSections == nil {
		return "", nil
	}

	var wg sync.WaitGroup
	htmlParts := make([]string, len(paginatedSections))
	errChan := make(chan error, len(paginatedSections))
	sem := make(chan struct{}, maxWorkers)

	for i, pageSections := range paginatedSections {
		wg.Add(1)
		sem <- struct{}{}
		go func(i int, pageData []domain.Section) {
			defer wg.Done()
			defer func() { <-sem }()

			buf := bufPool.Get().(*bytes.Buffer)
			defer bufPool.Put(buf)
			buf.Reset()

			if err := tmpl.ExecuteTemplate(buf, "page", pageData); err != nil {
				errChan <- err
				return
			}
			htmlParts[i] = buf.String()
		}(i, pageSections)
	}

	wg.Wait()
	close(errChan)

	if len(errChan) > 0 {
		return "", <-errChan
	}

	return template.HTML(strings.Join(htmlParts, "")), nil
}

// GenerateHTML generates the final HTML from the report data.
func GenerateHTML(reportData domain.ReportData) (string, error) {
	// Create a new template with a function map and parse the HTML template file
	tmpl, err := template.New("").Funcs(helpers.GetTemplateFuncMap()).ParseGlob("templates/reports/pdf/*")
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse HTML template")
		return "", err
	}

	// Render pages in parallel
	sectionsHTML, err := RenderPagesParallel(tmpl, reportData.Sections)
	if err != nil {
		log.Error().Err(err).Msg("Failed to render pages in parallel")
		return "", err
	}

	// Create a new struct for the main template
	fullReportData := struct {
		domain.ReportData
		SectionsHTML template.HTML
	}{
		ReportData:   reportData,
		SectionsHTML: sectionsHTML,
	}

	// Execute the template with the report data
	var buf bytes.Buffer
	if err := tmpl.ExecuteTemplate(&buf, "index.tmpl", fullReportData); err != nil {
		log.Error().Err(err).Msg("Failed to execute HTML template. Generating error PDF.")
		// If template execution fails, generate a simple error HTML
		errorHTML := `<!DOCTYPE html><html><body><h1>Error Generating Report</h1><p>An error occurred while processing the report template:</p><pre>` + template.HTMLEscapeString(err.Error()) + `</pre></body></html>`
		return errorHTML, nil
	}

	// Save HTML output for debugging during development
	if err := os.WriteFile("debug_output.html", buf.Bytes(), 0644); err != nil {
		log.Error().Err(err).Msg("Failed to save debug HTML file")
	}

	return buf.String(), nil
}
