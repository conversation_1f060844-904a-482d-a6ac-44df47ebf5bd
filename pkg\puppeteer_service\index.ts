import { connectNodeAdapter, ConnectNodeAdapterOptions } from '@connectrpc/connect-node';
import { ConnectRouter, ServiceImpl } from '@connectrpc/connect';
import { PdfGeneratorService } from './proto/html2pdf/v1/service_pb';
import puppeteer, { <PERSON>rowser } from 'puppeteer';
import express from 'express';
import { Code, ConnectError } from '@connectrpc/connect';
import { create } from '@bufbuild/protobuf';
import { GenerateRequest, GenerateResponseSchema } from './proto/html2pdf/v1/model_pb';

let browser: Browser;

async function* generatePdf(req: GenerateRequest): AsyncGenerator<any> {
  if (!browser) {
    throw new ConnectError('Browser not initialized', Code.Internal);
  }

  try {
    const startTime = Date.now();
    const page = await browser.newPage();

    await page.setViewport({ width: 1200, height: 800 });
    await page.setContent(req.html, {
      waitUntil: 'domcontentloaded',
      timeout: 30000,
    });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      timeout: 1800000,
    });
    await page.close();

    const generationTime = Date.now() - startTime;
    console.log(`PDF generated in ${generationTime}ms, size: ${pdfBuffer.length} bytes`);

    const chunkSize = 64 * 1024;
    for (let i = 0; i < pdfBuffer.length; i += chunkSize) {
      const chunk = pdfBuffer.slice(i, i + chunkSize);
      yield create(GenerateResponseSchema, { chunk });
    }
  } catch (err) {
    console.error('PDF generation error:', err);
    throw new ConnectError((err as Error).message, Code.Internal);
  }
}

async function main() {
  try {
    browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    console.log('Browser initialized');

    // Pure Connect RPC server: mount at root, no REST endpoints
    const app = express();
    const routes = (router: ConnectRouter) =>
      router.service(PdfGeneratorService, {
        generate: generatePdf,
      } as ServiceImpl<typeof PdfGeneratorService>);
    const connectNodeOptions: ConnectNodeAdapterOptions = { routes };

    // Mount Connect RPC handler at root ('/')
    app.use('/', connectNodeAdapter(connectNodeOptions));

    //health check endpoint
    app.get('/health', (req, res) => {
      res.status(200).send('OK');
    });

    app.listen(3001, '0.0.0.0', () => {
      console.log('Connect RPC server running on port 3001 (pure RPC, no REST)');
    });
  } catch (err) {
    console.error('Failed to initialize browser or start server:', err);
    process.exit(1);
  }
}

main();

process.on('SIGINT', async () => {
  if (browser) {
    await browser.close();
  }
  process.exit();
});

process.on('SIGTERM', async () => {
  if (browser) {
    await browser.close();
  }
  process.exit();
});
