package services

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/rs/zerolog/log"

	html2pdfv1 "snap-printer/pkg/printing_service/proto/html2pdf/v1"
	html2pdfv1connect "snap-printer/pkg/printing_service/proto/html2pdf/v1/html2pdfv1connect"

	"connectrpc.com/connect"
)

var (
	puppeteerAddr    = "http://puppeteer:3001"
	puppeteerHTTPURL = "http://puppeteer:3001/pdf"
	useStreaming     = true
)

func init() {
	if addr := os.Getenv("PUPPETEER_GRPC_ADDR"); addr != "" {
		puppeteerAddr = addr
	}
	if url := os.Getenv("PUPPETEER_URL"); url != "" {
		puppeteerHTTPURL = url
	}
	if os.<PERSON>env("ENABLE_GRPC") == "false" {
		useStreaming = false
	}
	log.Info().Str("PUPPETEER_GRPC_ADDR", puppeteerAddr).Bool("streaming", useStreaming).Msg("Initializing Puppeteer client")
}

// GeneratePDFFromHTML sends the HTML to the Puppeteer service to generate a PDF.
// Uses streaming gRPC for better performance with large content.
func GeneratePDFFromHTML(html string) ([]byte, error) {
	return generatePDFStreaming(html)
}

// generatePDFStreaming uses connect gRPC to generate PDF via Puppeteer service
func generatePDFStreaming(html string) ([]byte, error) {
	log.Info().Str("puppeteer_grpc_addr", puppeteerAddr).Msg("Connecting to Puppeteer gRPC service for PDF generation")
	client := html2pdfv1connect.NewPdfGeneratorServiceClient(
		http.DefaultClient,
		puppeteerAddr,
		connect.WithGRPC(), // Ensure we use gRPC, not gRPC-Web
	)

	req := connect.NewRequest(&html2pdfv1.GenerateRequest{
		Html: html,
	})
	log.Debug().Msg("Sending Generate PDF gRPC request")
	stream, err := client.Generate(context.Background(), req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to start gRPC stream to Puppeteer service")
		return nil, fmt.Errorf("failed to call generate: %w", err)
	}
	var pdfData bytes.Buffer
	for stream.Receive() {
		chunk := stream.Msg().Chunk
		if len(chunk) > 0 {
			pdfData.Write(chunk)
			log.Debug().Int("chunk_size", len(chunk)).Msg("Received PDF chunk from gRPC stream")
		}
	}
	if err := stream.Err(); err != nil {
		log.Error().Err(err).Msg("gRPC stream error from Puppeteer service")
		return nil, fmt.Errorf("stream error: %w", err)
	}
	if pdfData.Len() == 0 {
		log.Error().Msg("No PDF data received from Puppeteer gRPC service")
		return nil, errors.New("no PDF data received from puppeteer service (gRPC)")
	}
	log.Info().Int("pdf_size_bytes", pdfData.Len()).Msg("Successfully generated PDF via gRPC")
	return pdfData.Bytes(), nil
}

// httpClientWithCompression provides HTTP client with gzip compression support
type httpClientWithCompression struct {
	client *http.Client
}

func (c *httpClientWithCompression) Do(req *http.Request) (*http.Response, error) {
	if c.client == nil {
		c.client = &http.Client{Timeout: 60 * time.Second}
	}
	req.Header.Set("Accept-Encoding", "gzip")
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}

	// Handle gzip decompression
	if resp.Header.Get("Content-Encoding") == "gzip" {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			resp.Body.Close()
			return nil, err
		}
		resp.Body = &gzipReadCloser{gzReader, resp.Body}
	}

	return resp, nil
}

type gzipReadCloser struct {
	gzReader *gzip.Reader
	original io.ReadCloser
}

func (g *gzipReadCloser) Read(p []byte) (int, error) {
	return g.gzReader.Read(p)
}

func (g *gzipReadCloser) Close() error {
	g.gzReader.Close()
	return g.original.Close()
}

// createCompressedHTTPRequest creates an HTTP request with optional gzip compression
func createCompressedHTTPRequest(ctx context.Context, html string) (*http.Request, error) {
	payload := map[string]string{"html": html}
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Compress large payloads
	var body io.Reader
	contentType := "application/json"
	contentEncoding := ""

	if len(payloadBytes) > 1024 { // Compress if larger than 1KB
		var buf bytes.Buffer
		gzWriter := gzip.NewWriter(&buf)
		_, err = gzWriter.Write(payloadBytes)
		if err != nil {
			return nil, fmt.Errorf("failed to compress payload: %w", err)
		}
		err = gzWriter.Close()
		if err != nil {
			return nil, fmt.Errorf("failed to close compressor: %w", err)
		}
		body = &buf
		contentEncoding = "gzip"
		log.Debug().Int("original_size", len(payloadBytes)).Int("compressed_size", buf.Len()).Msg("Compressed request payload")
	} else {
		body = strings.NewReader(string(payloadBytes))
	}

	req, err := http.NewRequestWithContext(ctx, "POST", puppeteerHTTPURL, body)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", contentType)
	if contentEncoding != "" {
		req.Header.Set("Content-Encoding", contentEncoding)
	}

	return req, nil
}
