# Snap Printing Service

## Visão Geral

O Snap Printing Service é uma aplicação para geração de relatórios em PDF a partir de dados estruturados, utilizando templates Go para renderização de HTML e o Puppeteer para conversão do HTML em PDF. O sistema é altamente modular, com arquitetura baseada em estratégias e templates parciais, garantindo flexibilidade e fácil manutenção.

---

## Arquitetura e Topologia

- **Backend Go**: Responsável por receber requisições, processar dados, renderizar HTML via templates Go e orquestrar a geração de PDFs.
- **Puppeteer Service**: Serviço Node.js isolado em container, responsável por converter HTML em PDF usando o Chromium headless.
- **Redis**: Utilizado para cache e controle de jobs.
- **Assets**: Imagens, fontes e CSS são servidos via HTTP pelo backend Go, garantindo que o Puppeteer tenha acesso aos recursos necessários para renderização.

### Topologia Visual (Mermaid)

```mermaid
flowchart TD
    Usuario(["Usuário"])
    GoAPI(["Go API (porta 8080)"])
    Redis(["Redis"])
    Puppeteer(["Puppeteer (porta 3001)"])
    Assets(["Assets/CSS"])

    Usuario <--> GoAPI
    GoAPI <--> Redis
    GoAPI <--> Puppeteer
    GoAPI --> Assets
    Puppeteer <--> Assets

    subgraph "Docker Compose"
        GoAPI
        Redis
        Puppeteer
    end
```

### Topologia Docker Compose

```
[Usuário] ⇄ [Go API (porta 8080)] ⇄ [Redis] ⇄ [Puppeteer (porta 3001)]
                                 ⇓
                             [Assets/CSS]
```

- Todos os serviços rodam em containers isolados, comunicando-se via rede interna do Docker Compose.

---

## Fluxo de Geração de PDF

1. **Recepção de Dados**: O backend Go recebe os dados do relatório via API.
2. **Renderização HTML**: Os dados são passados para os templates Go (`.tmpl`), que geram o HTML final.
3. **Envio ao Puppeteer**: O HTML é enviado via gRPC ou HTTP para o serviço Puppeteer.
4. **Conversão em PDF**: O Puppeteer renderiza o HTML e retorna o PDF gerado.
5. **Entrega**: O PDF é retornado ao usuário ou salvo conforme a necessidade.

---

## Organização dos Templates

- `templates/reports/pdf/index.tmpl`: Template principal, orquestra a renderização das páginas e inclui os parciais.
- `templates/reports/pdf/RenderPrint*.tmpl`: Templates parciais para cada seção do relatório, seguindo o padrão de estratégia.
- Todos os templates usam apenas sintaxe Go (`{{ ... }}`), sem JSX ou lógica não suportada.

---

## Gerenciamento de Assets

- Imagens e CSS ficam em `/assets` e `/templates/reports/pdf/styles.css`.
- O backend Go serve esses arquivos via rotas HTTP (`/assets`, `/templates/reports/pdf/styles.css`).
- O Puppeteer acessa os assets via HTTP durante a renderização do HTML.

---

## Estrutura de Pastas

```
.
├── cmd/printing_service/         # Entrypoint do serviço Go
├── pkg/printing_service/services # Lógica de renderização e integração Puppeteer
├── templates/reports/pdf/        # Templates Go (.tmpl)
├── assets/                       # Imagens, fontes, etc.
├── Dockerfile.go-api             # Dockerfile do backend Go
├── Dockerfile.puppeteer          # Dockerfile do Puppeteer
├── docker-compose.yml            # Orquestração dos serviços
└── README.md
```

---

## Variáveis de Ambiente Importantes

- `REDIS_ADDR`: Endereço do Redis (ex: `redis:6379`)
- `PUPPETEER_URL`: URL HTTP do Puppeteer (ex: `http://puppeteer:3001/pdf`)
- `PUPPETEER_GRPC_ADDR`: Endereço gRPC do Puppeteer (ex: `http://puppeteer:3001`)
- `GIN_MODE`: Modo do servidor Go (`release` recomendado)

---

## Build e Execução

### Usando Docker Compose

```sh
docker-compose up --build
```

- Acesse a API Go em `http://localhost:8080`
- O Puppeteer estará disponível internamente para a API Go

### Build Manual

```sh
docker build -f Dockerfile.go-api -t snap-printing-go .
docker build -f Dockerfile.puppeteer -t snap-printing-puppeteer .
docker run --rm -p 8080:8080 snap-printing-go
```

---

## Pontos de Extensão e Manutenção

- **Novas Seções**: Para adicionar uma nova seção ao relatório, crie um novo template parcial `.tmpl` e registre o struct correspondente no Go.
- **Estilos**: Edite `styles.css` para alterar a aparência dos relatórios.
- **Assets**: Adicione imagens/fontes em `/assets` e garanta que estejam acessíveis via HTTP.
- **Templates**: Use apenas sintaxe Go, sem lógica customizada ou criativa.
- **Fila de Processamento**: Implementar um sistema de filas para gerenciar requisições ao Puppeteer, evitando sobrecarga, permitindo priorização, e habilitando notificações de conclusão e acompanhamento de status.

---

## Testes

O projeto inclui scripts de teste na pasta `cmd/test` para validar a funcionalidade de geração de PDF:

- `test-pdf-generate.ps1` (PowerShell) e `test-pdf-generate.sh` (Bash): Scripts para testar o endpoint de geração de PDF
- `generate-pdf-request.json` e `reports-pdf-data.json`: Arquivos de dados de exemplo para testes
- `test-output.pdf`: Exemplo de saída gerada pelos testes

Os scripts de teste medem o desempenho da geração de PDF, incluindo tempo de processamento e tamanho do arquivo resultante, ajudando a identificar gargalos e otimizar o serviço.

---

## Eficiência e Economia de Recursos

O Snap Printing Service foi projetado para ser eficiente mesmo em máquinas com recursos limitados:

- **Renderização Otimizada**: O Puppeteer é configurado para usar o mínimo de recursos necessários
- **Instância Única de Browser**: Reutiliza a mesma instância do Chromium para múltiplas requisições
- **Streaming de Dados**: Transferência em chunks para gerenciar o uso de memória
- **Configuração Leve**: Flags otimizadas para reduzir o consumo de recursos

Essa eficiência permite executar o serviço em ambientes com recursos limitados, gerando economia significativa em infraestrutura.

---

## Benefícios do Cache

O sistema implementa um cache sofisticado usando Redis, proporcionando:

- **Redução de Tempo de Resposta**: PDFs já gerados são servidos instantaneamente
- **Economia de Recursos Computacionais**: Evita re-renderização de documentos idênticos
- **Compressão Inteligente**: Comprime automaticamente PDFs grandes (>1KB) quando há benefício
- **Políticas de Expiração**: Gerenciamento automático do ciclo de vida dos dados em cache
- **Estatísticas de Uso**: Rastreamento de acessos para análise e otimização

O cache pode reduzir o tempo de resposta de segundos para milissegundos e diminuir significativamente a carga no servidor.

