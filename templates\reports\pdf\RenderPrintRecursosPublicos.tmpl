{{/*
  RenderPrintRecursosPublicos.tmpl
  Expects struct:
  type RecursosPublicosSection struct {
    Funds []struct {
      Source      string
      Amount      string
      Date        string
      Description string
      // Add other fields as needed
    }
  }
*/}}

<div class="recursos-publicos-section">
  <h2 class="section-header">RECURSOS PÚBLICOS RECEBIDOS</h2>
  {{if .Funds}}
    <table class="recursos-publicos-table">
      <thead>
        <tr>
          <th>Fonte</th>
          <th>Valor</th>
          <th>Data</th>
          <th>Descrição</th>
        </tr>
      </thead>
      <tbody>
        {{range .Funds}}
        <tr>
          <td>{{.Source}}</td>
          <td>{{.Amount}}</td>
          <td>{{.Date}}</td>
          <td>{{.Description}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum recurso encontrado.</div>
  {{end}}
</div> 