{{/*
  RenderPrintParentes.tmpl
  Expects struct:
  type ParentesSection struct {
    Relatives []struct {
      Name         string
      Relationship string
      Age          int
      Notes        string
      // Add other fields as needed
    }
  }
*/}}

<div class="parentes-section">
  <h2 class="section-header">PARENTES</h2>
  {{if .Relatives}}
    <table class="parentes-table">
      <thead>
        <tr>
          <th>Nome</th>
          <th>Parentesco</th>
          <th>Idade</th>
          <th>Observações</th>
        </tr>
      </thead>
      <tbody>
        {{range .Relatives}}
        <tr>
          <td>{{.Name}}</td>
          <td>{{.Relationship}}</td>
          <td>{{.Age}}</td>
          <td>{{.Notes}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum parente encontrado.</div>
  {{end}}
</div> 