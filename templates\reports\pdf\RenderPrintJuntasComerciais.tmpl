{{/*
  RenderPrintJuntasComerciais.tmpl
  Expects struct:
  type JuntasComerciaisSection struct {
    Records []struct {
      Company      string
      Registration string
      Date         string
      Status       string
      // Add other fields as needed
    }
  }
*/}}

<div class="juntas-comerciais-section">
  <h2 class="section-header">JUNTAS COMERCIAIS</h2>
  {{if .Records}}
    <table class="juntas-comerciais-table">
      <thead>
        <tr>
          <th>Empresa</th>
          <th>Registro</th>
          <th>Data</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {{range .Records}}
        <tr>
          <td>{{.Company}}</td>
          <td>{{.Registration}}</td>
          <td>{{.Date}}</td>
          <td>{{.Status}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum registro encontrado.</div>
  {{end}}
</div> 