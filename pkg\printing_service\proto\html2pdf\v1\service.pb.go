// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: html2pdf/v1/service.proto

package html2pdfv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_html2pdf_v1_service_proto protoreflect.FileDescriptor

const file_html2pdf_v1_service_proto_rawDesc = "" +
	"\n" +
	"\x19html2pdf/v1/service.proto\x12\vhtml2pdf.v1\x1a\x17html2pdf/v1/model.proto2`\n" +
	"\x13PdfGeneratorService\x12I\n" +
	"\bGenerate\x12\x1c.html2pdf.v1.GenerateRequest\x1a\x1d.html2pdf.v1.GenerateResponse0\x01B@Z>snap-printer/pkg/printing_service/proto/html2pdf/v1;html2pdfv1b\x06proto3"

var file_html2pdf_v1_service_proto_goTypes = []any{
	(*GenerateRequest)(nil),  // 0: html2pdf.v1.GenerateRequest
	(*GenerateResponse)(nil), // 1: html2pdf.v1.GenerateResponse
}
var file_html2pdf_v1_service_proto_depIdxs = []int32{
	0, // 0: html2pdf.v1.PdfGeneratorService.Generate:input_type -> html2pdf.v1.GenerateRequest
	1, // 1: html2pdf.v1.PdfGeneratorService.Generate:output_type -> html2pdf.v1.GenerateResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_html2pdf_v1_service_proto_init() }
func file_html2pdf_v1_service_proto_init() {
	if File_html2pdf_v1_service_proto != nil {
		return
	}
	file_html2pdf_v1_model_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_html2pdf_v1_service_proto_rawDesc), len(file_html2pdf_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_html2pdf_v1_service_proto_goTypes,
		DependencyIndexes: file_html2pdf_v1_service_proto_depIdxs,
	}.Build()
	File_html2pdf_v1_service_proto = out.File
	file_html2pdf_v1_service_proto_goTypes = nil
	file_html2pdf_v1_service_proto_depIdxs = nil
}
