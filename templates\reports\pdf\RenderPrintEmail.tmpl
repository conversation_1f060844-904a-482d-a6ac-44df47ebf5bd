{{/*
  RenderPrintEmail.tmpl
  Expects struct:
  type EmailSection struct {
    Emails []struct {
      Email    string
      Type     string
      Verified bool
      // Add other fields as needed
    }
  }
*/}}

<div class="emails-section">
  <h2 class="section-header">EMAILS</h2>
  {{if .Emails}}
    <table class="emails-table">
      <thead>
        <tr>
          <th>Email</th>
          <th>Tipo</th>
          <th>Verificado</th>
        </tr>
      </thead>
      <tbody>
        {{range .Emails}}
        <tr>
          <td>{{.Email}}</td>
          <td>{{.Type}}</td>
          <td>{{if .Verified}}Sim{{else}}Não{{end}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum email encontrado.</div>
  {{end}}
</div> 