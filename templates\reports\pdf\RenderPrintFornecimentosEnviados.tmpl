{{/*
  RenderPrintFornecimentosEnviados.tmpl
  Expects struct:
  type FornecimentosEnviadosSection struct {
    Supplies []struct {
      Recipient   string
      Amount      string
      Date        string
      Description string
      // Add other fields as needed
    }
  }
*/}}

<div class="fornecimentos-enviados-section">
  <h2 class="section-header">FORNECIMENTOS ENVIADOS</h2>
  {{if .Supplies}}
    <table class="fornecimentos-enviados-table">
      <thead>
        <tr>
          <th>Destinatário</th>
          <th>Valor</th>
          <th>Data</th>
          <th>Descrição</th>
        </tr>
      </thead>
      <tbody>
        {{range .Supplies}}
        <tr>
          <td>{{.Recipient}}</td>
          <td>{{.Amount}}</td>
          <td>{{.Date}}</td>
          <td>{{.Description}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum fornecimento enviado encontrado.</div>
  {{end}}
</div> 