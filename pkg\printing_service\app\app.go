package app

import (
	"log"
	"net/http"
	_ "net/http/pprof"

	"snap-printer/pkg/printing_service/web"
)

type App struct {
	srv *web.Server
}

func New() (*App, error) {

	// init web layer
	srv, err := web.New()
	if err != nil {
		return nil, err
	}

	return &App{
		srv: srv,
	}, nil
}

func (a *App) Start() {
	go func() {
		log.Println(http.ListenAndServe("localhost:6060", nil)) // Run pprof server
	}()
	a.srv.Run()
}
