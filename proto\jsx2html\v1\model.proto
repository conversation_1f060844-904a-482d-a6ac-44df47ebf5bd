syntax = "proto3";

package jsx2html.v1;

import "google/protobuf/struct.proto";

option go_package = "snap-printer/pkg/printing_service/proto/jsx2html/v1;jsx2htmlv1";

message GenerateRequest {
  ReportMetadata metadata = 1;
  repeated ReportSection sections = 2;
}

message ReportMetadata {
  string report_id = 1 [json_name="report_id"];
  string report_status = 2 [json_name="report_status"];
  string report_type = 3 [json_name="report_type"];
  google.protobuf.Struct report_search_args = 4 [json_name="report_search_args"];
  string report_name = 5 [json_name="report_name"];
  string creation_at = 6 [json_name="creation_at"];
  string modified_at = 7 [json_name="modified_at"];
  string subject_name = 8 [json_name="subject_name"];
  string subject_mother_name = 9 [json_name="subject_mother_name"];
  int32 subject_age = 10 [json_name="subject_age"];
  string subject_sex = 11 [json_name="subject_sex"];
}

message ReportSection {
  string title = 1;
  string subtitle = 2;
  string subsection = 3;
  repeated string source = 4;
  int32 data_count = 5 [json_name="data_count"];
  bool is_deleted = 6 [json_name="is_deleted"];
  repeated google.protobuf.Struct data = 7;
}

message GenerateResponse {
  string html = 1;
}
