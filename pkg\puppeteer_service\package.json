{"name": "puppeteer-service", "version": "1.0.0", "type": "module", "description": "Puppeteer service for PDF generation", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "build": "tsc", "dev": "tsc -w"}, "dependencies": {"@bufbuild/protobuf": "^2.5.2", "@connectrpc/connect": "^2.0.2", "@connectrpc/connect-node": "^2.0.2", "compression": "^1.8.0", "express": "^5.1.0", "puppeteer": "^24.11.2"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/express": "^5.0.3", "@types/node": "^24.0.10", "typescript": "^5.8.3"}}