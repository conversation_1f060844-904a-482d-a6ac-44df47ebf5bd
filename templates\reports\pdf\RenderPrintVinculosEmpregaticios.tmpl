{{/*
  RenderPrintVinculosEmpregaticios.tmpl
  Expects struct:
  type VinculosEmpregaticiosSection struct {
    Employments []struct {
      Company   string
      Position  string
      StartDate string
      EndDate   string
      Status    string
      // Add other fields as needed
    }
  }
*/}}

<div class="vinculos-empregaticios-section">
  <h2 class="section-header">VÍNCULOS EMPREGATÍCIOS</h2>
  {{if .Employments}}
    <table class="vinculos-empregaticios-table">
      <thead>
        <tr>
          <th>Empresa</th>
          <th>Cargo</th>
          <th>Início</th>
          <th>Fim</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {{range .Employments}}
        <tr>
          <td>{{.Company}}</td>
          <td>{{.Position}}</td>
          <td>{{.StartDate}}</td>
          <td>{{.EndDate}}</td>
          <td>{{.Status}}</td>
        </tr>
        {{end}}
      </tbody>
    </table>
  {{else}}
    <div class="empty-message">Nenhum vínculo empregatício encontrado.</div>
  {{end}}
</div> 