package io

import (
	"net/http"

	domain "snap-printer/pkg/printing_service/domain/reports/pdf"
	"snap-printer/pkg/printing_service/services"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

func GeneratePDF(c *gin.Context) {
	var reportData domain.ReportData
	if err := c.ShouldBindJSON(&reportData); err != nil {
		log.Error().Err(err).Msg("Failed to bind JSON data")
		c.<PERSON>SO<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate the HTML
	html, err := services.Jsx2Html(reportData)
	if err != nil {
		log.Error().Err(err).Msg("Failed to generate HTML")
		c.<PERSON>SO<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Check cache first
	if pdfBytes, found := services.CheckCache(html); found {
		c<PERSON>("Content-Type", "application/pdf")
		c<PERSON><PERSON>("Content-Disposition", "attachment; filename=report.pdf")
		c.<PERSON>(http.StatusOK, "application/pdf", pdfBytes)
		return
	}

	// If not in cache, generate the PDF from the HTML
	pdfBytes, err := services.GeneratePDFFromHTML(html)
	if err != nil {
		log.Error().Err(err).Msg("Failed to generate PDF")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Write the newly generated PDF to the cache
	if err := services.WriteCache(html, pdfBytes); err != nil {
		log.Error().Err(err).Msg("Failed to write to cache")
		// We don't fail the request if caching fails, just log it
	}

	// Set the headers and write the PDF to the response
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "attachment; filename=report.pdf")
	c.Data(http.StatusOK, "application/pdf", pdfBytes)
}
