// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file react2pdf/v1/model.proto (package react2pdf.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file react2pdf/v1/model.proto.
 */
export const file_react2pdf_v1_model: GenFile = /*@__PURE__*/
  fileDesc("ChhyZWFjdDJwZGYvdjEvbW9kZWwucHJvdG8SDHJlYWN0MnBkZi52MSIgCg9HZW5lcmF0ZVJlcXVlc3QSDQoFcmVhY3QYASABKAkiIQoQR2VuZXJhdGVSZXNwb25zZRINCgVjaHVuaxgBIAEoDEIvWi1wcmludGluZy1zZXJ2aWNlL3BrZy9yZWFjdDJwZGYvdjE7cmVhY3QycGRmdjFiBnByb3RvMw");

/**
 * @generated from message react2pdf.v1.GenerateRequest
 */
export type GenerateRequest = Message<"react2pdf.v1.GenerateRequest"> & {
  /**
   * @generated from field: string react = 1;
   */
  react: string;
};

/**
 * Describes the message react2pdf.v1.GenerateRequest.
 * Use `create(GenerateRequestSchema)` to create a new message.
 */
export const GenerateRequestSchema: GenMessage<GenerateRequest> = /*@__PURE__*/
  messageDesc(file_react2pdf_v1_model, 0);

/**
 * @generated from message react2pdf.v1.GenerateResponse
 */
export type GenerateResponse = Message<"react2pdf.v1.GenerateResponse"> & {
  /**
   * @generated from field: bytes chunk = 1;
   */
  chunk: Uint8Array;
};

/**
 * Describes the message react2pdf.v1.GenerateResponse.
 * Use `create(GenerateResponseSchema)` to create a new message.
 */
export const GenerateResponseSchema: GenMessage<GenerateResponse> = /*@__PURE__*/
  messageDesc(file_react2pdf_v1_model, 1);

